import React from 'react';
import { 
  Card, 
  But<PERSON>, 
  Row, 
  Col, 
  Descriptions, 
  Statistic, 
  Tag, 
  Breadcrumb, 
  Spin, 
  Divider,
  Space
} from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  ShopOutlined, 
  EnvironmentOutlined, 
  ArrowLeftOutlined,
  HomeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InboxOutlined,
  EditOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useWarehouse } from '../../hooks/useWarehouses';

const ViewWarehouse = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  
  const { data: warehouse, isLoading, error } = useWarehouse(id);

  const handleEdit = () => {
    navigate(`/warehouses/edit/${id}`);
  };

  const handleBack = () => {
    navigate('/warehouses');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const getStatusTag = (status) => {
    if (status === 'active') {
      return <Tag color="success" icon={<CheckCircleOutlined />}>Hoạt động</Tag>;
    }
    return <Tag color="error" icon={<CloseCircleOutlined />}>Ngừng hoạt động</Tag>;
  };

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !warehouse) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '48px' }}>
          <h3>Không tìm thấy kho hàng</h3>
          <p>Kho hàng không tồn tại hoặc đã bị xóa.</p>
          <Button type="primary" onClick={handleBack}>
            Quay lại danh sách
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* Breadcrumb */}
      <Breadcrumb style={{ marginBottom: 24 }}>
        <Breadcrumb.Item>
          <HomeOutlined />
          <span>Trang chủ</span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <ShopOutlined />
          <span>Kho hàng</span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>Chi tiết kho hàng</Breadcrumb.Item>
      </Breadcrumb>

      {/* Header */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
              <ShopOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
              <div>
                <h2 style={{ margin: 0, fontSize: '24px' }}>{warehouse.ten_kho}</h2>
                <p style={{ margin: 0, color: '#666' }}>ID: {warehouse.id}</p>
              </div>
            </div>
          </Col>
          <Col>
            <Space>
              <Button 
                icon={<ArrowLeftOutlined />} 
                onClick={handleBack}
              >
                Quay lại
              </Button>
              <Button 
                type="primary"
                icon={<EditOutlined />} 
                onClick={handleEdit}
              >
                Chỉnh sửa
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card hoverable>
            <Statistic
              title="Tổng sản phẩm"
              value={warehouse.tong_san_pham || 0}
              prefix={<ShopOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '28px' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card hoverable>
            <Statistic
              title="Tổng tồn kho"
              value={warehouse.tong_ton_kho || 0}
              prefix={<InboxOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '28px' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card hoverable>
            <Statistic
              title="Giá trị tồn kho"
              value={warehouse.gia_tri_ton_kho || 0}
              prefix="₫"
              valueStyle={{ color: '#722ed1', fontSize: '28px' }}
              formatter={(value) => value.toLocaleString('vi-VN')}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card hoverable>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#666', fontSize: '14px', marginBottom: '12px' }}>
                Trạng thái
              </div>
              <div style={{ fontSize: '18px' }}>
                {getStatusTag(warehouse.trang_thai)}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Main Information */}
      <Card title="Thông tin chi tiết">
        <Descriptions bordered column={2} size="middle">
          <Descriptions.Item 
            label={<><ShopOutlined /> Tên kho hàng</>}
            span={2}
          >
            <strong style={{ fontSize: '16px' }}>{warehouse.ten_kho}</strong>
          </Descriptions.Item>
          
          <Descriptions.Item 
            label={<><EnvironmentOutlined /> Địa chỉ</>}
            span={2}
          >
            {warehouse.dia_chi}
          </Descriptions.Item>
          
          <Descriptions.Item 
            label={<><FileTextOutlined /> Mô tả</>}
            span={2}
          >
            {warehouse.mo_ta || 'Không có mô tả'}
          </Descriptions.Item>
          
          <Descriptions.Item label={<><UserOutlined /> Người tạo</>}>
            {warehouse.nguoi_tao || 'N/A'}
          </Descriptions.Item>
          
          <Descriptions.Item label={<><CalendarOutlined /> Ngày tạo</>}>
            {formatDate(warehouse.ngay_tao)}
          </Descriptions.Item>
          
          <Descriptions.Item label={<><UserOutlined /> Người cập nhật</>}>
            {warehouse.nguoi_cap_nhap || 'N/A'}
          </Descriptions.Item>
          
          <Descriptions.Item label={<><CalendarOutlined /> Ngày cập nhật</>}>
            {formatDate(warehouse.ngay_cap_nhap)}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* Additional Statistics */}
      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={12}>
          <Card title="Thông tin tồn kho" size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Tổng số sản phẩm">
                <strong>{warehouse.tong_san_pham || 0}</strong> sản phẩm
              </Descriptions.Item>
              <Descriptions.Item label="Tổng số lượng tồn">
                <strong>{warehouse.tong_ton_kho || 0}</strong> đơn vị
              </Descriptions.Item>
              <Descriptions.Item label="Giá trị tồn kho">
                <strong style={{ color: '#52c41a' }}>
                  {((warehouse.gia_tri_ton_kho || 0)).toLocaleString('vi-VN')} VNĐ
                </strong>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Trạng thái hoạt động" size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Trạng thái hiện tại">
                {getStatusTag(warehouse.trang_thai)}
              </Descriptions.Item>
              <Descriptions.Item label="Thời gian hoạt động">
                {warehouse.ngay_tao ? 
                  `${Math.ceil((new Date() - new Date(warehouse.ngay_tao)) / (1000 * 60 * 60 * 24))} ngày` : 
                  'N/A'
                }
              </Descriptions.Item>
              <Descriptions.Item label="Lần cập nhật cuối">
                {warehouse.ngay_cap_nhap ? 
                  formatDate(warehouse.ngay_cap_nhap) : 
                  'Chưa cập nhật'
                }
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ViewWarehouse;
