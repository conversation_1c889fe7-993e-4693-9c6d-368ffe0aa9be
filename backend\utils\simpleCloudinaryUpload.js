const cloudinary = require('../config/cloudinary');

/**
 * Simple upload function without complex transformations
 */
async function simpleUploadImage(fileInput, options = {}) {
  try {
    const {
      folder = 'general',
      public_id,
      width,
      height,
    } = options;

    console.log('🚀 Starting simple upload...', {
      folder,
      width,
      height,
      hasFile: !!fileInput
    });

    // Basic upload options
    const uploadOptions = {
      folder: `salesys/${folder}`,
      resource_type: 'image',
    };

    // Add public_id if provided
    if (public_id) {
      uploadOptions.public_id = public_id;
    }

    // Add basic transformation if width/height provided
    if (width || height) {
      uploadOptions.transformation = {
        width: width,
        height: height,
        crop: 'fill'
      };
    }

    console.log('📤 Upload options:', uploadOptions);

    let uploadResult;

    // Upload based on input type
    if (Buffer.isBuffer(fileInput)) {
      // Upload from buffer
      uploadResult = await new Promise((resolve, reject) => {
        cloudinary.uploader.upload_stream(
          uploadOptions,
          (error, result) => {
            if (error) {
              console.error('❌ Upload stream error:', error);
              reject(error);
            } else {
              console.log('✅ Upload stream success:', result);
              resolve(result);
            }
          }
        ).end(fileInput);
      });
    } else if (typeof fileInput === 'string') {
      // Upload from file path or URL
      console.log('📁 Uploading from path/URL:', fileInput);
      uploadResult = await cloudinary.uploader.upload(fileInput, uploadOptions);
      console.log('✅ Upload from path success:', uploadResult);
    } else {
      throw new Error('File input must be Buffer or string path');
    }

    // Create simple URLs without complex transformations
    const baseUrl = uploadResult.secure_url;
    
    // Simple thumbnail URL
    const thumbnailUrl = cloudinary.url(uploadResult.public_id, {
      width: 200,
      height: 200,
      crop: 'fill'
    });

    // Simple optimized URL
    const optimizedUrl = cloudinary.url(uploadResult.public_id, {
      width: width || 800,
      height: height || 600,
      crop: 'fill'
    });

    const result = {
      success: true,
      data: {
        public_id: uploadResult.public_id,
        url: baseUrl,
        optimized_url: optimizedUrl,
        thumbnail_url: thumbnailUrl,
        width: uploadResult.width,
        height: uploadResult.height,
        format: uploadResult.format,
        size: uploadResult.bytes,
        created_at: uploadResult.created_at
      }
    };

    console.log('🎉 Simple upload completed:', result);
    return result;

  } catch (error) {
    console.error('❌ Simple upload error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  simpleUploadImage
};
