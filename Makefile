# SellPro Docker Management

.PHONY: help build up down logs clean dev dev-down prod-build prod-up prod-down migrate seed

# Default target
help:
	@echo "SellPro Docker Commands:"
	@echo "  make dev          - Start development environment"
	@echo "  make dev-down     - Stop development environment"
	@echo "  make prod-build   - Build production images"
	@echo "  make prod-up      - Start production environment"
	@echo "  make prod-down    - Stop production environment"
	@echo "  make logs         - View logs"
	@echo "  make migrate      - Run database migrations"
	@echo "  make seed         - Run database seeders"
	@echo "  make clean        - Clean up containers and volumes"
	@echo "  make help         - Show this help"

# Development commands
dev:
	@echo "🚀 Starting development environment..."
	docker-compose -f docker-compose.dev.yml up --build

dev-down:
	@echo "🛑 Stopping development environment..."
	docker-compose -f docker-compose.dev.yml down

# Production commands
prod-build:
	@echo "🏗️  Building production images..."
	docker-compose build

prod-up:
	@echo "🚀 Starting production environment..."
	docker-compose up -d

prod-down:
	@echo "🛑 Stopping production environment..."
	docker-compose down

# Utility commands
logs:
	@echo "📋 Viewing logs..."
	docker-compose logs -f

logs-dev:
	@echo "📋 Viewing development logs..."
	docker-compose -f docker-compose.dev.yml logs -f

migrate:
	@echo "🗄️  Running database migrations..."
	docker-compose exec backend npm run migrate

migrate-dev:
	@echo "🗄️  Running database migrations (dev)..."
	docker-compose -f docker-compose.dev.yml exec backend-dev npm run migrate

seed:
	@echo "🌱 Running database seeders..."
	docker-compose exec backend npm run seed

seed-dev:
	@echo "🌱 Running database seeders (dev)..."
	docker-compose -f docker-compose.dev.yml exec backend-dev npm run seed

# Cleanup commands
clean:
	@echo "🧹 Cleaning up containers and volumes..."
	docker-compose down -v
	docker-compose -f docker-compose.dev.yml down -v
	docker system prune -f

clean-all:
	@echo "🧹 Cleaning up everything..."
	docker-compose down -v --rmi all
	docker-compose -f docker-compose.dev.yml down -v --rmi all
	docker system prune -af

# Database commands
db-reset:
	@echo "🔄 Resetting database..."
	docker-compose down db
	docker volume rm sellpro_db-data
	docker-compose up -d db

db-reset-dev:
	@echo "🔄 Resetting development database..."
	docker-compose -f docker-compose.dev.yml down db-dev
	docker volume rm sellpro_db-dev-data
	docker-compose -f docker-compose.dev.yml up -d db-dev
