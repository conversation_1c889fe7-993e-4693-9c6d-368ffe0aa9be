'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('tag_nguoi_dung', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      nguoi_dung_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      tag_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'tag',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      }
    });

    // Add indexes
    await queryInterface.addIndex('tag_nguoi_dung', ['nguoi_dung_id'], {
      name: 'idx_tag_nguoi_dung_nguoi_dung_id'
    });
    await queryInterface.addIndex('tag_nguoi_dung', ['tag_id'], {
      name: 'idx_tag_nguoi_dung_tag_id'
    });
    
    // Add unique constraint for combination
    await queryInterface.addIndex('tag_nguoi_dung', ['nguoi_dung_id', 'tag_id'], {
      unique: true,
      name: 'tag_nd_unique'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('tag_nguoi_dung');
  }
};
