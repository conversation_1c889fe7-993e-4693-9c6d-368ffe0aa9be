'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('phien_ban_san_pham', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      san_pham_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'san_pham',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      ten_phien_ban: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ma: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true,
        comment: 'Mã sản phẩm không trùng lặp, để trống thì tự động sinh theo quy tắc'
      },
      ma_vach: {
        type: Sequelize.STRING,
        allowNull: true
      },
      mo_ta: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      khoi_luong: {
        type: Sequelize.DOUBLE,
        allowNull: true
      },
      don_vi_tinh: {
        type: Sequelize.STRING,
        allowNull: true
      },
      gia_le: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      gia_buon: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      gia_nhap: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      trang_thai: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: '1: active, 0: inactive'
      },
      anh_phien_ban: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_tao: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('phien_ban_san_pham', ['san_pham_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('phien_ban_san_pham');
  }
};
