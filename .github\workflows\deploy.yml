name: Deploy to VPS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy to VPS via SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.VPS_HOST }}
          username: root
          key: ${{ secrets.VPS_SSH_KEY }}
          script: |
            # Di chuyển vào thư mục đã clone trên VPS
            cd /root/SELLPRO

            # Kéo code mới nhất về
            git pull origin main

            # Dừng container cũ nếu đang chạy
            docker compose down

            # Build và chạy lại
            docker compose up -d --build
