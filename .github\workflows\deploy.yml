name: Deploy to VPS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy to VPS via SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.VPS_HOST }}
          username: root # Hoặc username c<PERSON><PERSON> bạn trên VPS
          key: ${{ secrets.VPS_SSH_KEY }}
          script: |
            # Navigate to the project directory on your VPS
            # Thay 'your-project-directory' bằng tên thư mục project của bạn
            cd /root/your-project-directory 

            # Pull the latest changes from the main branch
            git pull origin main

            # Stop and remove old containers, and build and run new ones
            docker-compose down
            docker-compose up -d --build
