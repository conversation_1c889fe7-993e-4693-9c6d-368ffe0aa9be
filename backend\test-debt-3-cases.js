require('dotenv').config();
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sequelize } = require('./models');

async function testDebt3Cases() {
  try {
    console.log('🧪 Testing 3 Debt Cases...\n');

    // 1. Tạo khách hàng test
    console.log('1️⃣ Creating test customer...');
    const customer = await NguoiDung.create({
      ho_ten: 'Nguyễn Văn Test 3 Cases',
      so_dien_thoai: '0987654888',
      email: '<EMAIL>',
      loai_nguoi_dung: 'khach_hang',
      trang_thai: 'dang_giao_dich'
    });
    console.log(`✅ Created customer: ${customer.ho_ten} (ID: ${customer.id})`);

    // 2. Tạo đơn hàng đã xác nhận (công nợ dự tính)
    console.log('\n2️⃣ Creating confirmed order (predicted debt)...');
    const confirmedOrder = await DonHang.create({
      ma_don_hang: `CONFIRMED-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'da_xac_nhan', // Đã xác nhận
      tong_tien: 1000000, // Tổng tiền sản phẩm
      tong_phai_tra: 500000, // Tiền COD
      tong_da_tra: 0, // Chưa thanh toán
      con_phai_tra: 300000, // Công nợ (1M - 500K - 200K cọc)
      tien_cod: 500000, // Tiền COD
      tien_coc: 200000, // Tiền cọc
      ghi_chu: 'Confirmed order - predicted debt'
    });
    console.log(`✅ Created confirmed order: ${confirmedOrder.ma_don_hang}`);

    // 3. Tạo đơn hàng đã giao thành công (công nợ thực tế)
    console.log('\n3️⃣ Creating completed order (actual debt)...');
    const completedOrder = await DonHang.create({
      ma_don_hang: `COMPLETED-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'hoan_thanh', // Đã giao thành công
      tong_tien: 800000, // Tổng tiền sản phẩm
      tong_phai_tra: 400000, // Tiền COD
      tong_da_tra: 0, // Chưa thanh toán
      con_phai_tra: 200000, // Công nợ (800K - 400K - 200K cọc)
      tien_cod: 400000, // Tiền COD
      tien_coc: 200000, // Tiền cọc
      ghi_chu: 'Completed order - actual debt'
    });
    console.log(`✅ Created completed order: ${completedOrder.ma_don_hang}`);

    // 4. Tạo đơn hàng hoàn (công nợ = 0)
    console.log('\n4️⃣ Creating returned order (debt = 0)...');
    const returnedOrder = await DonHang.create({
      ma_don_hang: `RETURNED-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'hoan_hang', // Đã hoàn hàng
      tong_tien: 600000, // Tổng tiền sản phẩm
      tong_phai_tra: 300000, // Tiền COD
      tong_da_tra: 0, // Chưa thanh toán
      con_phai_tra: 150000, // Công nợ gốc (600K - 300K - 150K cọc)
      tien_cod: 300000, // Tiền COD
      tien_coc: 150000, // Tiền cọc
      ghi_chu: 'Returned order - debt should be 0'
    });
    console.log(`✅ Created returned order: ${returnedOrder.ma_don_hang}`);

    // 5. Test query và tính toán
    console.log('\n5️⃣ Testing debt calculation...');
    const customers = await NguoiDung.findAll({
      where: { id: customer.id },
      include: [
        {
          model: DonHang,
          as: "donHangKhachHang",
          attributes: [
            "id",
            "ma_don_hang",
            "tong_tien",
            "tong_phai_tra",
            "tong_da_tra",
            "con_phai_tra",
            "tien_cod",
            "tien_coc",
            "ngay_ban",
            "trang_thai",
          ],
          required: false,
        },
      ],
    });

    const testCustomer = customers[0];
    const orders = testCustomer.donHangKhachHang || [];

    // Định nghĩa trạng thái
    const confirmedStatuses = ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh'];
    const completedStatuses = ['hoan_thanh'];
    const returnedStatuses = ['hoan_hang'];

    // Tính công nợ hiện tại (dự tính)
    const currentDebt = orders.reduce((sum, order) => {
      if (returnedStatuses.includes(order.trang_thai)) {
        return sum; // Đơn hàng hoàn = 0
      }
      if (confirmedStatuses.includes(order.trang_thai)) {
        return sum + (parseFloat(order.con_phai_tra) || 0);
      }
      return sum;
    }, 0);

    // Tính công nợ thực tế
    const actualDebt = orders.reduce((sum, order) => {
      if (returnedStatuses.includes(order.trang_thai)) {
        return sum; // Đơn hàng hoàn = 0
      }
      if (completedStatuses.includes(order.trang_thai)) {
        return sum + (parseFloat(order.con_phai_tra) || 0);
      }
      return sum;
    }, 0);

    console.log('📊 Calculation Results:');
    console.log(`   - Current Debt (predicted): ${currentDebt.toLocaleString('vi-VN')} VND`);
    console.log(`   - Expected: ${(300000 + 200000).toLocaleString('vi-VN')} VND (confirmed + completed)`);
    console.log(`   - Actual Debt (completed only): ${actualDebt.toLocaleString('vi-VN')} VND`);
    console.log(`   - Expected: ${(200000).toLocaleString('vi-VN')} VND (completed only)`);

    // 6. Test order data format
    console.log('\n6️⃣ Testing order data format...');
    orders.forEach(order => {
      const debtAmount = returnedStatuses.includes(order.trang_thai) 
        ? 0 
        : (order.con_phai_tra || 0);
      
      console.log(`   Order ${order.ma_don_hang}:`);
      console.log(`     - Status: ${order.trang_thai}`);
      console.log(`     - Original Debt: ${(order.con_phai_tra || 0).toLocaleString('vi-VN')} VND`);
      console.log(`     - Effective Debt: ${debtAmount.toLocaleString('vi-VN')} VND`);
      console.log(`     - Is Returned: ${returnedStatuses.includes(order.trang_thai)}`);
      console.log(`     - Is Completed: ${completedStatuses.includes(order.trang_thai)}`);
      console.log(`     - Counts in Current: ${confirmedStatuses.includes(order.trang_thai) && !returnedStatuses.includes(order.trang_thai)}`);
      console.log(`     - Counts in Actual: ${completedStatuses.includes(order.trang_thai) && !returnedStatuses.includes(order.trang_thai)}`);
    });

    // 7. Cleanup
    console.log('\n7️⃣ Cleaning up...');
    await confirmedOrder.destroy();
    await completedOrder.destroy();
    await returnedOrder.destroy();
    await customer.destroy();
    console.log('✅ Cleanup completed');

    // 8. Verify results
    console.log('\n8️⃣ Verification:');
    const expectedCurrent = 300000 + 200000; // confirmed + completed
    const expectedActual = 200000; // completed only
    
    if (currentDebt === expectedCurrent && actualDebt === expectedActual) {
      console.log('🎉 All tests passed! 3 debt cases working correctly.');
    } else {
      console.log('❌ Tests failed!');
      console.log(`   Current: ${currentDebt} (expected: ${expectedCurrent})`);
      console.log(`   Actual: ${actualDebt} (expected: ${expectedActual})`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy test
testDebt3Cases();
