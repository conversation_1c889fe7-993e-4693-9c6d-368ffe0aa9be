# Development Dockerfile for Node.js backend
FROM node:18-alpine

# Install development tools
RUN apk add --no-cache curl bash netcat-openbsd git

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Create wait-for-it script
RUN echo '#!/bin/bash\n\
TIMEOUT=15\n\
QUIET=0\n\
HOST=$1\n\
PORT=$2\n\
shift 2\n\
CMD="$@"\n\
\n\
until nc -z $HOST $PORT; do\n\
  >&2 echo "Waiting for $HOST:$PORT..."\n\
  sleep 1\n\
done\n\
\n\
>&2 echo "$HOST:$PORT is available"\n\
exec $CMD' > /usr/local/bin/wait-for-it.sh && \
    chmod +x /usr/local/bin/wait-for-it.sh

# Expose port 5000
EXPOSE 5000

# Health check for development
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:5000/api/health || exit 1

# Start development server with nodemon
CMD ["sh", "-c", "wait-for-it.sh db 3306 -- npm run dev"]
