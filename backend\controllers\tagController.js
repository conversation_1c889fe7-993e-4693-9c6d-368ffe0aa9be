const { Tag, NguoiDungTag } = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');

/**
 * <PERSON><PERSON><PERSON> danh sách tags
 */
const getTags = async (req, res) => {
  const { search = '', page = 1, limit = 100 } = req.query;
  
  // Xây dựng điều kiện tìm kiếm
  const whereClause = {};
  if (search) {
    whereClause.ten = { [Op.like]: `%${search}%` };
  }
  
  // Tính toán phân trang
  const offset = (page - 1) * limit;
  
  // Truy vấn danh sách tags
  const { count, rows } = await Tag.findAndCountAll({
    where: whereClause,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['ten', 'ASC']]
  });
  
  res.json({
    success: true,
    data: rows,
    pagination: {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit)
    }
  });
};

/**
 * <PERSON><PERSON><PERSON> chi tiết tag
 */
const getTag = async (req, res) => {
  const { id } = req.params;
  
  const tag = await Tag.findByPk(id);
  if (!tag) {
    throw new AppError('Tag không tồn tại', 404);
  }
  
  res.json({
    success: true,
    data: tag
  });
};

/**
 * Tạo tag mới
 */
const createTag = async (req, res) => {
  const { ten, mau_sac, mo_ta } = req.body;
  
  if (!ten) {
    throw new AppError('Tên tag là bắt buộc', 400);
  }
  
  // Kiểm tra tag đã tồn tại chưa
  const existingTag = await Tag.findOne({
    where: { ten }
  });
  
  if (existingTag) {
    throw new AppError('Tag đã tồn tại', 400);
  }
  
  // Tạo tag mới
  const tag = await Tag.create({
    ten,
    mau_sac: mau_sac || '#1890ff',
    mo_ta: mo_ta || '',
    nguoi_tao: req.user?.username || 'system'
  });
  
  res.status(201).json({
    success: true,
    message: 'Tạo tag thành công',
    data: tag
  });
};

/**
 * Cập nhật tag
 */
const updateTag = async (req, res) => {
  const { id } = req.params;
  const { ten, mau_sac, mo_ta } = req.body;
  
  const tag = await Tag.findByPk(id);
  if (!tag) {
    throw new AppError('Tag không tồn tại', 404);
  }
  
  if (ten) {
    // Kiểm tra tên tag mới đã tồn tại chưa
    const existingTag = await Tag.findOne({
      where: {
        ten,
        id: { [Op.ne]: id }
      }
    });
    
    if (existingTag) {
      throw new AppError('Tên tag đã tồn tại', 400);
    }
  }
  
  // Cập nhật tag
  await tag.update({
    ten: ten || tag.ten,
    mau_sac: mau_sac || tag.mau_sac,
    mo_ta: mo_ta !== undefined ? mo_ta : tag.mo_ta,
    nguoi_cap_nhap: req.user?.username || 'system',
    ngay_cap_nhap: new Date()
  });
  
  res.json({
    success: true,
    message: 'Cập nhật tag thành công',
    data: tag
  });
};

/**
 * Xóa tag
 */
const deleteTag = async (req, res) => {
  const { id } = req.params;
  
  const tag = await Tag.findByPk(id);
  if (!tag) {
    throw new AppError('Tag không tồn tại', 404);
  }
  
  // Kiểm tra tag đã được sử dụng chưa
  const usedTag = await NguoiDungTag.findOne({
    where: { tag_id: id }
  });
  
  if (usedTag) {
    throw new AppError('Tag đang được sử dụng, không thể xóa', 400);
  }
  
  // Xóa tag
  await tag.destroy();
  
  res.json({
    success: true,
    message: 'Xóa tag thành công'
  });
};

module.exports = {
  getTags,
  getTag,
  createTag,
  updateTag,
  deleteTag
};
