import React from 'react';
import { Card, Button, Space, Alert } from 'antd';
import { useNavigate } from 'react-router-dom';
import { EyeOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';

const TestNavigation = () => {
  const navigate = useNavigate();

  const testRoutes = [
    {
      path: '/products',
      label: 'Danh sách sản phẩm',
      icon: <EyeOutlined />,
      description: 'Trang danh sách tất cả sản phẩm'
    },
    {
      path: '/products/create',
      label: 'Tạo sản phẩm mới',
      icon: <PlusOutlined />,
      description: 'Trang tạo sản phẩm mới'
    },
    {
      path: '/products/detail/6',
      label: 'Xem chi tiết sản phẩm (ID: 6)',
      icon: <EyeOutlined />,
      description: 'Trang xem chi tiết sản phẩm'
    },
    {
      path: '/products/edit/6',
      label: 'Sửa sản phẩm (ID: 6)',
      icon: <EditOutlined />,
      description: 'Trang chỉnh sửa sản phẩm'
    }
  ];

  return (
    <Card title="🧪 Test Navigation - Product Pages" style={{ maxWidth: 800, margin: '20px auto' }}>
      <Alert
        message="Test Navigation"
        description="Click các nút bên dưới để test navigation đến các trang sản phẩm"
        type="info"
        style={{ marginBottom: 20 }}
      />

      <Space direction="vertical" style={{ width: '100%' }}>
        {testRoutes.map((route, index) => (
          <Card 
            key={index}
            size="small" 
            style={{ background: '#f9f9f9' }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                  {route.icon} {route.label}
                </div>
                <div style={{ fontSize: 12, color: '#666' }}>
                  {route.description}
                </div>
                <div style={{ fontSize: 11, color: '#999', fontFamily: 'monospace' }}>
                  Route: {route.path}
                </div>
              </div>
              <Button 
                type="primary" 
                onClick={() => navigate(route.path)}
                icon={route.icon}
              >
                Test
              </Button>
            </div>
          </Card>
        ))}
      </Space>

      <Alert
        message="Lưu ý"
        description={
          <div>
            <p><strong>Những gì cần kiểm tra:</strong></p>
            <ul>
              <li>✅ <strong>Trang List:</strong> Hiển thị danh sách, nút View/Edit hoạt động</li>
              <li>✅ <strong>Trang Detail:</strong> Hiển thị thông tin, ảnh, variants</li>
              <li>✅ <strong>Trang Edit:</strong> Form đầy đủ như Create, có thể chỉnh sửa tất cả</li>
              <li>✅ <strong>Trang Create:</strong> Tạo sản phẩm mới với đầy đủ tính năng</li>
            </ul>

            <p><strong>Tính năng Edit Product cần test:</strong></p>
            <ul>
              <li>🔧 Chỉnh sửa thông tin cơ bản (tên, mã, mô tả)</li>
              <li>🏷️ Thay đổi category, brand, tag, trạng thái</li>
              <li>📸 Thêm/xóa/sửa ảnh sản phẩm</li>
              <li>📦 Thêm/xóa/sửa phiên bản sản phẩm</li>
              <li>🏪 Quản lý kho hàng và tồn kho</li>
              <li>🔧 Thêm/sửa thuộc tính sản phẩm</li>
              <li>📏 Quản lý đơn vị quy đổi</li>
              <li>💰 Cập nhật giá chung cho tất cả phiên bản</li>
            </ul>
            <p><strong>ID sản phẩm test:</strong> 6 (từ API test trước đó)</p>
          </div>
        }
        type="warning"
        style={{ marginTop: 20 }}
      />
    </Card>
  );
};

export default TestNavigation;
