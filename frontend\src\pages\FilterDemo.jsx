import React, { useState } from 'react';
import { Button, Card, Typography, Space } from 'antd';
import { FilterOutlined } from '@ant-design/icons';
import { FilterPanel } from '../components/Common';

const { Title, Text } = Typography;

const FilterDemo = () => {
  const [showFilter, setShowFilter] = useState(false);
  const [filterValues, setFilterValues] = useState({});

  const demoFilters = [
    // Thông tin cơ bản
    {
      key: 'category',
      label: 'Loại sản phẩm',
      type: 'select',
      category: 'Thông tin cơ bản',
      placeholder: 'Chọn loại sản phẩm',
      options: [
        { value: 'ao_thun', label: 'Áo thun' },
        { value: 'quan_jean', label: 'Quần jean' },
        { value: 'giay_dep', label: 'Giày dép' },
        { value: 'phu_kien', label: 'Phụ kiện' }
      ]
    },
    {
      key: 'brands',
      label: 'Nhãn hiệu',
      type: 'multiSelect',
      category: 'Thông tin cơ bản',
      placeholder: 'Chọn nhãn hiệu',
      options: [
        { value: 'adidas', label: 'Adidas' },
        { value: 'nike', label: 'Nike' },
        { value: 'puma', label: 'Puma' },
        { value: 'uniqlo', label: 'Uniqlo' }
      ]
    },
    {
      key: 'created_date',
      label: 'Ngày tạo sản phẩm',
      type: 'dateRange',
      category: 'Thông tin cơ bản'
    },
    
    // Giá và tồn kho
    {
      key: 'price_range',
      label: 'Khoảng giá bán',
      type: 'numberRange',
      category: 'Giá và tồn kho',
      min: 0,
      max: 10000000
    },
    {
      key: 'stock_quantity',
      label: 'Số lượng tồn kho',
      type: 'numberRange',
      category: 'Giá và tồn kho',
      min: 0
    },
    {
      key: 'stock_status',
      label: 'Trạng thái tồn kho',
      type: 'checkbox',
      category: 'Giá và tồn kho',
      options: [
        { value: 'in_stock', label: 'Còn hàng' },
        { value: 'out_of_stock', label: 'Hết hàng' },
        { value: 'low_stock', label: 'Sắp hết hàng' }
      ]
    },
    
    // Trạng thái và phân loại
    {
      key: 'product_status',
      label: 'Trạng thái sản phẩm',
      type: 'radio',
      category: 'Trạng thái',
      options: [
        { value: 'all', label: 'Tất cả' },
        { value: 'active', label: 'Đang hoạt động' },
        { value: 'inactive', label: 'Ngừng bán' },
        { value: 'pending', label: 'Chờ duyệt' }
      ]
    },
    {
      key: 'tax_applicable',
      label: 'Áp dụng thuế',
      type: 'select',
      category: 'Trạng thái',
      options: [
        { value: 'yes', label: 'Có áp dụng' },
        { value: 'no', label: 'Không áp dụng' }
      ]
    },
    
    // Thuộc tính sản phẩm
    {
      key: 'features',
      label: 'Tính năng đặc biệt',
      type: 'checkbox',
      category: 'Thuộc tính',
      options: [
        { value: 'waterproof', label: 'Chống nước' },
        { value: 'breathable', label: 'Thoáng khí' },
        { value: 'antibacterial', label: 'Kháng khuẩn' },
        { value: 'uv_protection', label: 'Chống tia UV' }
      ]
    },
    {
      key: 'size_range',
      label: 'Kích thước',
      type: 'multiSelect',
      category: 'Thuộc tính',
      options: [
        { value: 'xs', label: 'XS' },
        { value: 's', label: 'S' },
        { value: 'm', label: 'M' },
        { value: 'l', label: 'L' },
        { value: 'xl', label: 'XL' },
        { value: 'xxl', label: 'XXL' }
      ]
    }
  ];

  const handleFilter = (values) => {
    console.log('Filter applied:', values);
    setFilterValues(values);
  };

  const handleReset = () => {
    console.log('Filter reset');
    setFilterValues({});
  };

  return (
    <div style={{ padding: 24 }}>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={2}>FilterPanel Demo</Title>
            <Text type="secondary">
              Demo component FilterPanel với các loại filter khác nhau được phân nhóm theo category
            </Text>
          </div>

          <div>
            <Button 
              type="primary" 
              icon={<FilterOutlined />}
              onClick={() => setShowFilter(true)}
              size="large"
            >
              Mở bộ lọc nâng cao
            </Button>
          </div>

          <div>
            <Title level={4}>Giá trị filter hiện tại:</Title>
            <Card size="small" style={{ background: '#f5f5f5' }}>
              <pre style={{ margin: 0, fontSize: 12 }}>
                {JSON.stringify(filterValues, null, 2)}
              </pre>
            </Card>
          </div>
        </Space>
      </Card>

      <FilterPanel
        title="Bộ lọc sản phẩm nâng cao"
        visible={showFilter}
        filters={demoFilters}
        onFilter={handleFilter}
        onReset={handleReset}
        onClose={() => setShowFilter(false)}
        width={450}
      />
    </div>
  );
};

export default FilterDemo;
