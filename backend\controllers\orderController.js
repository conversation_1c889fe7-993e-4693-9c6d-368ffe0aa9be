const {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  San<PERSON><PERSON>,
  Nguoi<PERSON>,
  Ton<PERSON><PERSON><PERSON><PERSON>en<PERSON>an,
  Khach<PERSON><PERSON>,
  <PERSON><PERSON>,
  CongNoNguoiDung,
} = require("../models");
const { sequelize } = require("../models");
const { Op } = require("sequelize");
const { AppError } = require("../middleware/errorHandler");

// Lấy danh sách đơn hàng
const getOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      trang_thai,
      tu_ngay,
      den_ngay,
      khach_hang_id,
    } = req.query;

    const offset = (page - 1) * limit;
    let whereCondition = {};

    console.log("🔧 getOrders params:", {
      page,
      limit,
      search,
      trang_thai,
      tu_ngay,
      den_ngay,
    });

    // Tìm kiếm theo mã đơn hàng và ghi chú
    if (search) {
      whereCondition[Op.or] = [
        { ma_don_hang: { [Op.like]: `%${search}%` } },
        { ghi_chu: { [Op.like]: `%${search}%` } },
      ];
    }

    // Lọc theo trạng thái
    if (trang_thai) {
      whereCondition.trang_thai = trang_thai;
    }

    // Lọc theo khách hàng
    if (khach_hang_id) {
      whereCondition.khach_hang_id = khach_hang_id;
    }

    // Lọc theo ngày - kiểm tra valid date
    if (
      tu_ngay &&
      tu_ngay !== "undefined" &&
      den_ngay &&
      den_ngay !== "undefined"
    ) {
      const startDate = new Date(tu_ngay);
      const endDate = new Date(den_ngay);
      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        whereCondition.ngay_ban = {
          [Op.between]: [startDate, endDate],
        };
      }
    } else if (tu_ngay && tu_ngay !== "undefined") {
      const startDate = new Date(tu_ngay);
      if (!isNaN(startDate.getTime())) {
        whereCondition.ngay_ban = {
          [Op.gte]: startDate,
        };
      }
    } else if (den_ngay && den_ngay !== "undefined") {
      const endDate = new Date(den_ngay);
      if (!isNaN(endDate.getTime())) {
        whereCondition.ngay_ban = {
          [Op.lte]: endDate,
        };
      }
    }

    // Query với thông tin khách hàng và sản phẩm
    const { count, rows: orders } = await DonHang.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: NguoiDung,
          as: "khachHang",
          attributes: ["ho_ten", "so_dien_thoai"],
          required: false,
        },
        {
          model: DonHangSanPham,
          as: "sanPhamList",
          required: false,
        },
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [["id", "DESC"]],
    });

    // Tính toán thống kê
    const stats = await calculateOrderStats();

    // Không cần tính toán payments ở đây nữa vì sử dụng giá trị từ database

    // Format dữ liệu trả về với thông tin thật
    const formattedOrders = orders.map((order) => {
      // Sử dụng giá trị từ database thay vì tính toán lại
      const orderTotalAmount = order.tong_phai_tra || 0;
      const orderPaidAmount = order.tong_da_tra || 0;
      const remainingAmount = order.con_phai_tra || 0;

      return {
        id: order.id,
        ma_don_hang: order.ma_don_hang,
        khach_hang_id: order.khach_hang_id,
        ten_khach_hang: order.khachHang?.ho_ten || "Khách lẻ",
        so_dien_thoai: order.khachHang?.so_dien_thoai,
        ngay_ban: order.ngay_ban,
        trang_thai: order.trang_thai,
        tong_tien: order.tong_tien,
        chiet_khau: order.chiet_khau,
        tong_phai_tra: orderTotalAmount,
        tong_da_tra: orderPaidAmount,
        con_phai_tra: remainingAmount,
        tien_cod: order.tien_cod || 0, // Thêm trường tiền COD
        tien_coc: order.tien_coc || 0, // Thêm trường tiền cọc
        ghi_chu: order.ghi_chu,
        so_luong_san_pham: order.sanPhamList?.length || 0,
        nguoi_tao: order.nguoi_tao,
      };
    });

    res.json({
      success: true,
      data: formattedOrders,
      total: count,
      stats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit),
      },
    });
  } catch (error) {
    console.error("Error getting orders:", error);
    res.status(500).json({
      success: false,
      message: "Có lỗi xảy ra khi lấy danh sách đơn hàng",
    });
  }
};

// Lấy chi tiết đơn hàng
const getOrder = async (req, res) => {
  try {
    const { id } = req.params;

    const order = await DonHang.findByPk(id, {
      include: [
        {
          model: NguoiDung,
          as: "khachHang",
          attributes: ["ho_ten", "so_dien_thoai", "email"],
          required: false,
        },
        {
          model: DonHangSanPham,
          as: "sanPhamList",
          include: [
            {
              model: PhienBanSanPham,
              as: "phienBanSanPham",
              include: [
                {
                  model: SanPham,
                  as: "sanPham",
                  attributes: ["ten"],
                },
              ],
            },
          ],
          required: false,
        },
        {
          model: NguoiDung,
          as: "nhanVienBan",
          attributes: ["ho_ten", "email"],
          required: false,
        },
      ],
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy đơn hàng",
      });
    }

    // Format dữ liệu chi tiết
    const formattedOrder = {
      id: order.id,
      ma_don_hang: order.ma_don_hang,
      khach_hang: {
        ho_ten: order.khachHang?.ho_ten || "Khách lẻ",
        so_dien_thoai: order.khachHang?.so_dien_thoai,
        email: order.khachHang?.email,
      },
      ngay_ban: order.ngay_ban,
      trang_thai: order.trang_thai,
      nguon_don_hang: order.nguon_don_hang,
      tong_tien: order.tong_tien,
      chiet_khau: order.chiet_khau,
      tong_phai_tra: order.tong_phai_tra,
      tong_da_tra: order.tong_da_tra,
      con_phai_tra: order.con_phai_tra,
      tien_cod: order.tien_cod || 0, // Thêm trường tiền COD
      tien_coc: order.tien_coc || 0, // Thêm trường tiền cọc
      ghi_chu: order.ghi_chu,
      nguoi_tao: order.nguoi_tao,
      nhan_vien_ban: order.nhanVienBan?.ho_ten,
      san_pham_list:
        order.sanPhamList?.map((item) => ({
          id: item.id,
          ten_san_pham: item.ten_san_pham || item.phienBanSanPham?.sanPham?.ten,
          so_luong: item.so_luong,
          don_gia: item.don_gia,
          thanh_tien: item.thanh_tien,
          phien_ban_san_pham_id: item.phien_ban_san_pham_id,
        })) || [],
    };

    res.json({
      success: true,
      data: formattedOrder,
    });
  } catch (error) {
    console.error("Error getting order:", error);
    res.status(500).json({
      success: false,
      message: "Có lỗi xảy ra khi lấy thông tin đơn hàng",
    });
  }
};

// Tạo đơn hàng mới
const createOrder = async (req, res) => {
  const transaction = await DonHang.sequelize.transaction();

  try {
    const {
      khach_hang_id,
      ten_khach_hang,
      so_dien_thoai,
      dia_chi,
      ngay_dat_hang,
      trang_thai = "cho_xu_ly",
      ghi_chu,
      phuong_thuc_giao_hang,
      chi_tiet_don_hang,
      tong_tien_hang,
      giam_gia = 0,
      tong_thanh_toan,
      manual_total_amount, // Giá trị manual total gốc
      loai_giam_gia = "amount",
      is_manual_total = false, // Đánh dấu có phải tổng tiền thủ công không
      tien_coc = 0, // Tiền cọc
      tien_cod = 0, // Tiền COD
      cong_no = 0, // Công nợ
    } = req.body;

    console.log("📋 Creating order with data:", req.body);
    console.log("💰 Payment calculation debug:", {
      is_manual_total,
      tong_thanh_toan,
      manual_total_amount,
      tong_tien_hang,
      giam_gia,
      loai_giam_gia,
      tien_coc,
      tien_cod,
      cong_no,
    });

    // Validate chi tiết đơn hàng
    if (!chi_tiet_don_hang || chi_tiet_don_hang.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: "Đơn hàng phải có ít nhất một sản phẩm",
      });
    }

    // Validate khách hàng - cho phép khách lẻ (không cần khach_hang_id)
    // if (!khach_hang_id && !ten_khach_hang) {
    //   await transaction.rollback();
    //   return res.status(400).json({
    //     success: false,
    //     message: 'Vui lòng nhập thông tin khách hàng'
    //   });
    // }

    // Tạo mã đơn hàng
    const ma_don_hang = await generateOrderCode();

    // Tính tổng tiền từ chi tiết đơn hàng
    let calculated_total = 0;
    for (const item of chi_tiet_don_hang) {
      calculated_total += item.so_luong * item.gia_ban;
    }

    // Tính giảm giá
    const chiet_khau_amount =
      loai_giam_gia === "percent"
        ? (calculated_total * giam_gia) / 100
        : giam_gia;

    // Tính tổng tiền sản phẩm (sau giảm giá)
    const total_product_amount = is_manual_total
      ? manual_total_amount
      : calculated_total - chiet_khau_amount;

    // LOGIC MỚI theo yêu cầu:
    // 1. tong_phai_tra = tiền COD (số tiền cần thu từ khách)
    const tong_phai_tra = tien_cod;

    // 2. tong_da_tra = 0 (ban đầu chưa thanh toán gì, chỉ cập nhật khi có thanh toán thực tế)
    const tong_da_tra = 0;

    // 3. con_phai_tra = công nợ (tiền sản phẩm - tiền COD - tiền cọc)
    // Nếu âm = mình nợ khách, nếu dương = khách nợ mình
    const con_phai_tra = total_product_amount - tien_cod - tien_coc;

    // Công nợ tính toán (giống con_phai_tra)
    const calculated_debt = con_phai_tra;

    // Tạo ghi chú cho đơn hàng
    let order_notes = ghi_chu || "";

    // Thêm ghi chú về tổng tiền thủ công
    if (is_manual_total) {
      const manual_note = `[TỔNG TIỀN THỦ CÔNG] Tổng tiền được điều chỉnh thủ công: ${manual_total_amount.toLocaleString(
        "vi-VN"
      )}đ (Tính tự động: ${(
        calculated_total - chiet_khau_amount
      ).toLocaleString("vi-VN")}đ)`;
      order_notes = order_notes
        ? `${order_notes}\n${manual_note}`
        : manual_note;
    }

    // Thêm ghi chú về thanh toán
    if (tien_coc > 0 || tien_cod > 0) {
      const payment_notes = [];
      if (tien_coc > 0)
        payment_notes.push(`Tiền cọc: ${tien_coc.toLocaleString("vi-VN")}đ`);
      if (tien_cod > 0)
        payment_notes.push(`Tiền COD: ${tien_cod.toLocaleString("vi-VN")}đ`);

      const payment_note = `[THANH TOÁN] ${payment_notes.join(", ")}`;
      order_notes = order_notes
        ? `${order_notes}\n${payment_note}`
        : payment_note;
    }

    // Thêm ghi chú về công nợ
    if (calculated_debt !== 0) {
      const debt_note =
        calculated_debt > 0
          ? `[CÔNG NỢ] Khách còn nợ: ${calculated_debt.toLocaleString(
              "vi-VN"
            )}đ`
          : `[CÔNG NỢ] Mình nợ khách: ${Math.abs(
              calculated_debt
            ).toLocaleString("vi-VN")}đ`;
      order_notes = order_notes ? `${order_notes}\n${debt_note}` : debt_note;
    }

    // Tạo đơn hàng với field mapping đúng theo model
    const order = await DonHang.create(
      {
        ma_don_hang,
        khach_hang_id: khach_hang_id || null, // Cho phép null cho khách lẻ
        ngay_ban: ngay_dat_hang || new Date(),
        trang_thai,
        nguon_don_hang: phuong_thuc_giao_hang || "web", // Lưu delivery method vào nguon_don_hang
        tong_tien: total_product_amount, // Tổng tiền sản phẩm (sau giảm giá)
        chiet_khau: chiet_khau_amount, // Chiết khấu
        tong_phai_tra: tong_phai_tra, // = tiền COD (số tiền cần thu từ khách)
        tong_da_tra: tong_da_tra, // = 0 (ban đầu chưa thanh toán)
        con_phai_tra: con_phai_tra, // = công nợ (tiền SP - COD - cọc)
        tien_coc: tien_coc, // Tiền cọc
        tien_cod: tien_cod, // Tiền COD (CTV có thể bán giá cao hơn)
        ghi_chu: order_notes,
        nhan_vien_ban_id: null, // Tạm thời để null cho test API
        nguoi_tao: req.user?.username || "test",
      },
      { transaction }
    );

    // Tạo địa chỉ giao hàng nếu có thông tin
    if (ten_khach_hang || so_dien_thoai || dia_chi) {
      const { DonHangDiaChiGiao } = require("../models");
      await DonHangDiaChiGiao.create(
        {
          don_hang_id: order.id,
          dia_chi: dia_chi || "",
          nguoi_nhan: ten_khach_hang || "Khách lẻ",
          so_dien_thoai_nhan: so_dien_thoai || null,
        },
        { transaction }
      );

      console.log("📋 Delivery address created");
    }

    // Tạo chi tiết đơn hàng
    for (const item of chi_tiet_don_hang) {
      // Lấy thông tin sản phẩm để có tên đúng
      const { PhienBanSanPham, SanPham } = require("../models");
      const variant = await PhienBanSanPham.findByPk(
        item.phien_ban_san_pham_id,
        {
          include: [
            {
              model: SanPham,
              as: "sanPham",
              attributes: ["ten"],
            },
          ],
        }
      );

      const productName = variant
        ? `${variant.sanPham?.ten} - ${variant.ten_phien_ban}`
        : `Sản phẩm ${item.phien_ban_san_pham_id}`;

      await DonHangSanPham.create(
        {
          don_hang_id: order.id,
          phien_ban_san_pham_id: item.phien_ban_san_pham_id,
          ten_san_pham: productName,
          so_luong: item.so_luong,
          don_gia: item.gia_ban,
          thanh_tien: item.so_luong * item.gia_ban,
        },
        { transaction }
      );

      // Cập nhật tồn kho (trừ tồn kho) chỉ khi đơn hàng được xác nhận
      if (trang_thai === "da_xac_nhan" || trang_thai === "hoan_thanh") {
        await updateInventoryForOrder(
          item.phien_ban_san_pham_id,
          -item.so_luong,
          transaction
        );
      }
    }

    // 🟩 LOGIC CÔNG NỢ MỚI: Chỉ tính công nợ khi đơn hàng được xác nhận
    // ❌ Không tính công nợ cho: cho_xu_ly (draft), huy (cancelled)
    // ✅ Tính công nợ cho: da_xac_nhan, da_dong_goi, da_giao, hoan_thanh
    const shouldCreateDebt = [
      "da_xac_nhan",
      "da_dong_goi",
      "da_giao",
      "hoan_thanh",
    ].includes(trang_thai);

    if (khach_hang_id && calculated_debt !== 0 && shouldCreateDebt) {
      console.log(
        `🟩 Creating debt for confirmed order. Status: ${trang_thai}, Debt Amount: ${calculated_debt}`
      );
      await updateCustomerDebt(khach_hang_id, calculated_debt, transaction);
    } else if (khach_hang_id && calculated_debt !== 0 && !shouldCreateDebt) {
      console.log(
        `⚠️ Order created but debt not recorded. Status: ${trang_thai} (waiting for confirmation)`
      );
    }

    await transaction.commit();
    console.log("📋 Transaction committed successfully");

    // Trả về thông tin đơn hàng vừa tạo
    res.status(201).json({
      success: true,
      message: "Tạo đơn hàng thành công",
      data: {
        id: order.id,
        ma_don_hang: order.ma_don_hang,
        khach_hang_id: order.khach_hang_id,
        trang_thai: order.trang_thai,
        tong_tien: order.tong_tien,
        tong_phai_tra: order.tong_phai_tra,
        tong_da_tra: order.tong_da_tra,
        con_phai_tra: order.con_phai_tra,
        tien_coc: order.tien_coc,
        tien_cod: order.tien_cod,
        cong_no: calculated_debt,
        so_luong_san_pham: chi_tiet_don_hang.length,
        payment_summary: {
          product_total: total_product_amount, // Tổng tiền sản phẩm (sau giảm giá)
          deposit: tien_coc,
          cod_amount: tien_cod,
          debt: calculated_debt,
          debt_status:
            calculated_debt > 0
              ? "customer_owes"
              : calculated_debt < 0
              ? "we_owe_customer"
              : "balanced",
          note:
            calculated_debt < 0
              ? "CTV bán được giá cao hơn, mình nợ CTV"
              : calculated_debt > 0
              ? "CTV chưa thu đủ tiền"
              : "Cân bằng",
        },
      },
    });
  } catch (error) {
    await transaction.rollback();
    console.error("❌ Error creating order:", error);
    res.status(500).json({
      success: false,
      message: "Có lỗi xảy ra khi tạo đơn hàng",
      error: error.message,
    });
  }
};

// Cập nhật đơn hàng
const updateOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      ngay_cap_nhap: new Date(),
    };

    const [updatedRowsCount] = await DonHang.update(updateData, {
      where: { id },
    });

    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy đơn hàng",
      });
    }

    // Lấy đơn hàng đã cập nhật
    const updatedOrder = await DonHang.findByPk(id);

    res.json({
      success: true,
      message: "Cập nhật đơn hàng thành công",
      data: updatedOrder,
    });
  } catch (error) {
    console.error("Error updating order:", error);
    res.status(500).json({
      success: false,
      message: "Có lỗi xảy ra khi cập nhật đơn hàng",
    });
  }
};

// Helper functions
const generateOrderCode = async () => {
  const today = new Date();
  const dateStr = today.toISOString().slice(0, 10).replace(/-/g, "");

  const lastOrder = await DonHang.findOne({
    where: {
      ma_don_hang: {
        [Op.like]: `DH${dateStr}%`,
      },
    },
    order: [["ma_don_hang", "DESC"]],
  });

  let sequence = 1;
  if (lastOrder) {
    const lastSequence = parseInt(lastOrder.ma_don_hang.slice(-4));
    sequence = lastSequence + 1;
  }

  return `DH${dateStr}${sequence.toString().padStart(4, "0")}`;
};

const calculateOrderStats = async () => {
  const [totalOrders, pendingOrders, completedOrders, totalRevenue] =
    await Promise.all([
      // Tổng số đơn hàng
      DonHang.count(),

      // Đơn hàng chờ xác nhận
      DonHang.count({ where: { trang_thai: "cho_xu_ly" } }),

      // Đơn hàng đã hoàn thành
      DonHang.count({ where: { trang_thai: "hoan_thanh" } }),

      // Tổng doanh thu từ đơn hàng hoàn thành (tính theo tong_tien)
      DonHang.sum("tong_tien", {}),
    ]);

  return {
    total_orders: totalOrders || 0,
    pending_orders: pendingOrders || 0,
    completed_orders: completedOrders || 0,
    total_revenue: totalRevenue || 0,
  };
};

const updateInventoryForOrder = async (variantId, quantity, transaction) => {
  // Cập nhật tồn kho cho phiên bản sản phẩm
  const inventory = await TonKhoPhienBan.findOne({
    where: { phien_ban_san_pham_id: variantId },
  });

  if (inventory) {
    await inventory.update(
      {
        so_luong_ton: inventory.so_luong_ton + quantity,
      },
      { transaction }
    );
  }
};

// Cập nhật công nợ khách hàng khi tạo đơn hàng
const updateCustomerDebt = async (customerId, debtAmount, transaction) => {
  try {
    const { CongNoNguoiDung } = require("../models");

    console.log(
      `💰 Updating debt for customer ${customerId}, debt amount: ${debtAmount}`
    );
    console.log(
      `💡 Debt logic: Positive = Customer owes us, Negative = We owe customer`
    );

    // Tìm hoặc tạo bản ghi công nợ
    let debtRecord = await CongNoNguoiDung.findOne({
      where: { nguoi_dung_id: customerId },
    });

    if (!debtRecord) {
      // Tạo mới nếu chưa có
      const note =
        debtAmount > 0
          ? `Khách nợ ${debtAmount.toLocaleString("vi-VN")}đ từ đơn hàng`
          : `Mình nợ khách ${Math.abs(debtAmount).toLocaleString(
              "vi-VN"
            )}đ từ đơn hàng`;

      debtRecord = await CongNoNguoiDung.create(
        {
          nguoi_dung_id: customerId,
          tong_cong_no: debtAmount,
          ghi_chu: note,
        },
        { transaction }
      );

      console.log(
        `💰 Created new debt record: ${debtAmount} VND (${
          debtAmount > 0 ? "Customer owes us" : "We owe customer"
        })`
      );
    } else {
      // Cập nhật công nợ hiện tại
      const currentDebt = parseFloat(debtRecord.tong_cong_no || 0);
      const newDebt = currentDebt + debtAmount;

      await debtRecord.update(
        {
          tong_cong_no: newDebt,
        },
        { transaction }
      );

      console.log(
        `💰 Updated debt: ${currentDebt} + ${debtAmount} = ${newDebt} VND`
      );
      console.log(
        `💡 New debt status: ${
          newDebt > 0
            ? "Customer owes us"
            : newDebt < 0
            ? "We owe customer"
            : "Balanced"
        }`
      );
    }
  } catch (error) {
    console.error("❌ Error updating customer debt:", error);
    // Không throw error để không làm fail transaction tạo đơn hàng
  }
};

/**
 * Cập nhật trạng thái đơn hàng với logic công nợ
 */
const updateOrderStatus = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { trang_thai } = req.body;

    console.log(`🔄 Updating order ${id} status to: ${trang_thai}`);

    const order = await DonHang.findByPk(id, { transaction });
    if (!order) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy đơn hàng",
      });
    }

    const oldStatus = order.trang_thai;
    const newStatus = trang_thai;

    console.log(`📊 Status change: ${oldStatus} -> ${newStatus}`);

    // 🟩 LOGIC CÔNG NỢ THEO TRẠNG THÁI
    const debtStatuses = [
      "da_xac_nhan",
      "da_dong_goi",
      "da_giao",
      "hoan_thanh",
    ];
    const shouldHaveDebt = debtStatuses.includes(newStatus);
    const hadDebt = debtStatuses.includes(oldStatus);

    // Xử lý công nợ khi thay đổi trạng thái
    if (order.khach_hang_id && order.con_phai_tra > 0) {
      if (!hadDebt && shouldHaveDebt) {
        // Tạo công nợ khi chuyển từ draft -> confirmed
        console.log(
          `🟩 Creating debt: ${oldStatus} -> ${newStatus}, Amount: ${order.con_phai_tra}`
        );
        await updateCustomerDebtForStatusChange(
          order.khach_hang_id,
          order.con_phai_tra,
          "create",
          transaction
        );
      } else if (hadDebt && !shouldHaveDebt) {
        // Xóa công nợ khi hủy đơn
        console.log(
          `🔴 Removing debt: ${oldStatus} -> ${newStatus}, Amount: ${order.con_phai_tra}`
        );
        await updateCustomerDebtForStatusChange(
          order.khach_hang_id,
          order.con_phai_tra,
          "remove",
          transaction
        );
      }
    }

    // 🏭 XỬ LÝ TỒN KHO KHI THAY ĐỔI TRẠNG THÁI
    const inventoryStatuses = [
      "da_xac_nhan",
      "da_dong_goi",
      "da_giao",
      "hoan_thanh",
    ];
    const shouldReduceInventory = inventoryStatuses.includes(newStatus);
    const hadReducedInventory = inventoryStatuses.includes(oldStatus);

    // Lấy chi tiết đơn hàng để cập nhật tồn kho
    const orderDetails = await DonHangSanPham.findAll({
      where: { don_hang_id: id },
      transaction,
    });

    if (orderDetails && orderDetails.length > 0) {
      if (!hadReducedInventory && shouldReduceInventory) {
        // Trừ tồn kho khi chuyển từ draft -> confirmed
        console.log(`🏭 Reducing inventory: ${oldStatus} -> ${newStatus}`);
        for (const item of orderDetails) {
          await updateInventoryForOrder(
            item.phien_ban_san_pham_id,
            -item.so_luong, // Trừ tồn kho
            transaction
          );
        }
      } else if (hadReducedInventory && !shouldReduceInventory) {
        // Cộng lại tồn kho khi hủy đơn
        console.log(`🏭 Restoring inventory: ${oldStatus} -> ${newStatus}`);
        for (const item of orderDetails) {
          await updateInventoryForOrder(
            item.phien_ban_san_pham_id,
            item.so_luong, // Cộng lại tồn kho
            transaction
          );
        }
      }
    }

    // Cập nhật trạng thái đơn hàng
    await order.update(
      {
        trang_thai: newStatus,
        nguoi_cap_nhap: req.user?.username || "system",
        ngay_cap_nhap: new Date(),
      },
      { transaction }
    );

    await transaction.commit();
    console.log(`✅ Order ${id} status updated successfully with debt logic`);

    res.json({
      success: true,
      message: "Cập nhật trạng thái đơn hàng thành công",
      data: {
        order,
        debt_change: {
          old_status: oldStatus,
          new_status: newStatus,
          debt_amount: order.con_phai_tra,
          debt_action:
            !hadDebt && shouldHaveDebt
              ? "created"
              : hadDebt && !shouldHaveDebt
              ? "removed"
              : "no_change",
        },
      },
    });
  } catch (error) {
    await transaction.rollback();
    console.error("❌ Error updating order status:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

/**
 * Helper function for debt management when order status changes
 */
async function updateCustomerDebtForStatusChange(
  customerId,
  orderAmount,
  action,
  transaction
) {
  try {
    console.log(
      `💰 ${
        action === "create" ? "Creating" : "Removing"
      } debt for customer ${customerId}, amount: ${orderAmount}`
    );

    // Tìm hoặc tạo bản ghi công nợ
    let debtRecord = await CongNoNguoiDung.findOne({
      where: { nguoi_dung_id: customerId },
      transaction,
    });

    if (!debtRecord && action === "create") {
      // Tạo mới nếu chưa có và cần tạo công nợ
      debtRecord = await CongNoNguoiDung.create(
        {
          nguoi_dung_id: customerId,
          tong_cong_no: orderAmount,
          ghi_chu: "Tạo từ đơn hàng được xác nhận",
        },
        { transaction }
      );
      console.log(`💰 Created new debt record: ${orderAmount} VND`);
    } else if (debtRecord) {
      // Cập nhật công nợ hiện tại
      const currentDebt = parseFloat(debtRecord.tong_cong_no || 0);
      let newDebt;

      if (action === "create") {
        newDebt = currentDebt + orderAmount;
        console.log(
          `💰 Adding debt: ${currentDebt} + ${orderAmount} = ${newDebt} VND`
        );
      } else {
        newDebt = Math.max(0, currentDebt - orderAmount);
        console.log(
          `💰 Removing debt: ${currentDebt} - ${orderAmount} = ${newDebt} VND`
        );
      }

      await debtRecord.update(
        {
          tong_cong_no: newDebt,
        },
        { transaction }
      );
    }
  } catch (error) {
    console.error("❌ Error updating customer debt for status change:", error);
    throw error; // Re-throw để rollback transaction
  }
}

/**
 * Hoàn hàng - Cập nhật trạng thái, hoàn tồn kho và điều chỉnh công nợ
 */
const returnOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { ly_do_hoan_hang, ghi_chu_hoan_hang } = req.body;

    console.log(`🔄 Processing return for order ${id}`);

    // 1. Lấy thông tin đơn hàng và sản phẩm
    const order = await DonHang.findByPk(id, {
      include: [
        {
          model: DonHangSanPham,
          as: "sanPhamList",
          include: [
            {
              model: PhienBanSanPham,
              as: "phienBanSanPham",
              include: [
                {
                  model: SanPham,
                  as: "sanPham",
                },
              ],
            },
          ],
        },
      ],
      transaction,
    });

    if (!order) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy đơn hàng",
      });
    }

    // 2. Kiểm tra trạng thái có thể hoàn hàng (chỉ khi đã giao cho ĐVVC)
    if (order.trang_thai !== "da_giao") {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: `Không thể hoàn hàng đơn hàng có trạng thái: ${order.trang_thai}. Chỉ có thể hoàn hàng đơn đã giao cho đơn vị vận chuyển.`,
      });
    }

    console.log(
      `📦 Order has ${order.sanPhamList?.length || 0} products to return`
    );

    // 3. Hoàn tồn kho cho từng sản phẩm
    if (order.sanPhamList && order.sanPhamList.length > 0) {
      for (const orderProduct of order.sanPhamList) {
        const phienBanId = orderProduct.phien_ban_san_pham_id;
        const soLuongHoan = orderProduct.so_luong;

        console.log(
          `📦 Returning ${soLuongHoan} units of product variant ${phienBanId}`
        );

        // Tìm hoặc tạo record tồn kho
        const [tonKho, created] = await TonKhoPhienBan.findOrCreate({
          where: { phien_ban_san_pham_id: phienBanId },
          defaults: {
            phien_ban_san_pham_id: phienBanId,
            so_luong_ton: 0,
            so_luong_co_the_ban: 0,
            so_luong_dang_ve: 0,
          },
          transaction,
        });

        // Cập nhật tồn kho
        const newTonKho = (tonKho.so_luong_ton || 0) + soLuongHoan;
        const newCoTheBan = (tonKho.so_luong_co_the_ban || 0) + soLuongHoan;

        await tonKho.update(
          {
            so_luong_ton: newTonKho,
            so_luong_co_the_ban: newCoTheBan,
          },
          { transaction }
        );

        console.log(
          `✅ Updated inventory for variant ${phienBanId}: +${soLuongHoan} units (New total: ${newTonKho})`
        );
      }
    }

    // 4. Điều chỉnh công nợ nếu có
    if (order.khach_hang_id && order.con_phai_tra > 0) {
      console.log(
        `💰 Adjusting debt for customer ${order.khach_hang_id}: -${order.con_phai_tra} VND`
      );
      await updateCustomerDebtForStatusChange(
        order.khach_hang_id,
        order.con_phai_tra,
        "remove",
        transaction
      );
    }

    // 5. Cập nhật trạng thái đơn hàng
    await order.update(
      {
        trang_thai: "hoan_hang",
        ghi_chu: `${order.ghi_chu || ""}\n[HOÀN HÀNG] ${
          ly_do_hoan_hang || "Không có lý do"
        }\n${ghi_chu_hoan_hang || ""}`.trim(),
        nguoi_cap_nhap: req.user?.username || "system",
        ngay_cap_nhap: new Date(),
      },
      { transaction }
    );

    await transaction.commit();
    console.log(`✅ Order ${id} returned successfully`);

    res.json({
      success: true,
      message: "Hoàn hàng thành công",
      data: {
        order_id: id,
        old_status: order.trang_thai,
        new_status: "hoan_hang",
        returned_products: order.sanPhamList?.length || 0,
        debt_adjustment: order.con_phai_tra || 0,
        ly_do_hoan_hang,
        ghi_chu_hoan_hang,
      },
    });
  } catch (error) {
    await transaction.rollback();
    console.error("❌ Error processing order return:", error);
    res.status(500).json({
      success: false,
      message: "Có lỗi xảy ra khi hoàn hàng",
      error: error.message,
    });
  }
};

module.exports = {
  getOrders,
  getOrder,
  createOrder,
  updateOrder,
  updateOrderStatus,
  returnOrder,
};
