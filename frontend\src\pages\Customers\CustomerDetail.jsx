import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, Descriptions, Button, Tag, Divider, Row, Col, 
  Statistic, Space, Spin, Typography, Modal, message 
} from 'antd';
import { 
  EditOutlined, DeleteOutlined, ArrowLeftOutlined,
  UserOutlined, PhoneOutlined, MailOutlined, HomeOutlined,
  TeamOutlined, ClockCircleOutlined, DollarOutlined,
  ShoppingOutlined, ExclamationCircleOutlined,
  IdcardOutlined, GlobalOutlined, CalendarOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { customerAPI } from '../../services/api';
import CustomerForm from './CustomerForm';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { confirm } = Modal;

const CustomerDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);

  // Fetch customer data
  const { data, isLoading, error, refetch } = useQuery(
    ['customer', id],
    () => customerAPI.getCustomer(id),
    {
      enabled: !!id,
      refetchOnWindowFocus: false,
      onSuccess: (response) => {
        console.log('Customer data loaded:', response?.data?.data?.customer);
      },
      onError: (error) => {
        console.error('Error loading customer:', error);
        message.error('Không thể tải thông tin khách hàng');
      }
    }
  );

  const customer = data?.data?.data?.customer;
  
  // Tạo địa chỉ đầy đủ - đặt bên ngoài điều kiện để tránh vi phạm Rules of Hooks
  const fullAddress = customer ? [
    customer.dia_chi,
    customer.phuong_xa,
    customer.quan_huyen,
    customer.tinh_thanh
  ].filter(Boolean).join(', ') : '';

  // Delete customer mutation
  const deleteCustomerMutation = useMutation(
    () => customerAPI.deleteCustomer(id),
    {
      onSuccess: () => {
        message.success('Xóa khách hàng thành công');
        // Invalidate customers query cache để refresh danh sách khi quay lại
        queryClient.invalidateQueries(['customers']);
        navigate('/customers');
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa khách hàng');
      }
    }
  );

  // Tạo các hàm xử lý sự kiện bằng useCallback để tránh tạo lại hàm mỗi khi render
  const showDeleteConfirm = useCallback(() => {
    confirm({
      title: 'Bạn có chắc chắn muốn xóa khách hàng này?',
      icon: <ExclamationCircleOutlined />,
      content: 'Hành động này không thể hoàn tác.',
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      onOk() {
        deleteCustomerMutation.mutate();
      }
    });
  }, [deleteCustomerMutation]);

  const handleEditSuccess = useCallback(() => {
    setIsEditModalVisible(false);
    refetch();
    message.success('Cập nhật khách hàng thành công');
  }, [refetch]);

  // Debug effect - đặt ở đây để đảm bảo luôn được gọi
  useEffect(() => {
    if (customer) {
      console.log('Customer data loaded:', customer);
      console.log('nhomKhachHang:', customer.nhomKhachHang);
      console.log('nhanVienPhuTrach:', customer.nhanVienPhuTrach);
      console.log('ngay_sinh:', customer.ngay_sinh);
      console.log('gioi_tinh:', customer.gioi_tinh);
      console.log('ma_so_thue:', customer.ma_so_thue);
      console.log('website:', customer.website);
    }
  }, [customer]);

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !customer) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text type="danger">Không thể tải thông tin khách hàng</Text>
        <br />
        <Button onClick={() => navigate('/customers')} style={{ marginTop: '20px' }}>
          Quay lại danh sách
        </Button>
      </div>
    );
  }

  return (
    <>
      <Card
        title={
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/customers')}
            />
            <span>Chi tiết khách hàng</span>
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<EditOutlined />} 
              type="primary"
              onClick={() => setIsEditModalVisible(true)}
            >
              Chỉnh sửa
            </Button>
            <Button 
              icon={<DeleteOutlined />} 
              danger
              onClick={showDeleteConfirm}
              loading={deleteCustomerMutation.isLoading}
            >
              Xóa
            </Button>
          </Space>
        }
      >
        <Row gutter={[24, 24]}>
          <Col span={16}>
            <Card>
              <Title level={4}>
                <UserOutlined /> {customer?.ho_ten}
                <Tag 
                  color={
                    customer?.trang_thai === 'dang_giao_dich' ? 'green' : 
                    customer?.trang_thai === 'ngung_giao_dich' ? 'red' : 
                    customer?.trang_thai === 'tam_khoa' ? 'orange' : 'default'
                  } 
                  style={{ marginLeft: 12 }}
                >
                  {customer?.trang_thai === 'dang_giao_dich' ? 'Đang giao dịch' : 
                   customer?.trang_thai === 'ngung_giao_dich' ? 'Ngừng giao dịch' : 
                   customer?.trang_thai === 'tam_khoa' ? 'Tạm khóa' : 'Không xác định'}
                </Tag>
              </Title>
              <Text type="secondary">Mã khách hàng: {customer?.ma_khach_hang}</Text>

              <Divider />

              <Descriptions column={1} labelStyle={{ fontWeight: 'bold' }}>
                <Descriptions.Item label={<><PhoneOutlined /> Số điện thoại</>}>
                  {customer?.so_dien_thoai || 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label={<><MailOutlined /> Email</>}>
                  {customer?.email || 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label={<><HomeOutlined /> Địa chỉ</>}>
                  {fullAddress || 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label={<><TeamOutlined /> Nhóm khách hàng</>}>
                  {customer?.nhomKhachHang?.ten_nhom || 'Chưa phân nhóm'}
                </Descriptions.Item>
                <Descriptions.Item label={<><CalendarOutlined /> Ngày sinh</>}>
                  {customer?.ngay_sinh ? dayjs(customer.ngay_sinh).format('DD/MM/YYYY') : 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label="Giới tính">
                  {customer?.gioi_tinh === 'nam' ? 'Nam' : 
                   customer?.gioi_tinh === 'nu' ? 'Nữ' : 
                   customer?.gioi_tinh === 'khac' ? 'Khác' : 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label={<><IdcardOutlined /> Mã số thuế</>}>
                  {customer?.ma_so_thue || 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label={<><GlobalOutlined /> Website</>}>
                  {customer?.website ? (
                    <a href={customer.website.startsWith('http') ? customer.website : `https://${customer.website}`} 
                       target="_blank" rel="noopener noreferrer">
                      {customer.website}
                    </a>
                  ) : 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label={<><TeamOutlined /> Nhân viên phụ trách</>}>
                  {customer?.nhanVienPhuTrach?.ho_ten || 'Chưa phân công'}
                </Descriptions.Item>
                {customer?.ghi_chu && (
                  <Descriptions.Item label="Ghi chú">
                    {customer.ghi_chu}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          </Col>

          <Col span={8}>
            <Row gutter={[0, 16]}>
              <Col span={24}>
                <Card>
                  <Statistic
                    title="Tổng chi tiêu"
                    value={customer?.tong_chi_tieu || 0}
                    prefix={<DollarOutlined />}
                    suffix="đ"
                  />
                </Card>
              </Col>
              <Col span={24}>
                <Card>
                  <Statistic
                    title="Số đơn hàng"
                    value={customer?.tong_sl_don_hang || 0}
                    prefix={<ShoppingOutlined />}
                  />
                </Card>
              </Col>
              <Col span={24}>
                <Card>
                  <Statistic
                    title="Công nợ hiện tại"
                    value={customer?.cong_no_hien_tai || 0}
                    valueStyle={{ color: customer?.cong_no_hien_tai > 0 ? '#cf1322' : '#3f8600' }}
                    prefix={<DollarOutlined />}
                    suffix="đ"
                  />
                </Card>
              </Col>
            </Row>
          </Col>
        </Row>

        <Divider />

        <Descriptions column={2} labelStyle={{ fontWeight: 'bold' }}>
          <Descriptions.Item label={<><ClockCircleOutlined /> Ngày tạo</>}>
            {customer?.ngay_tao ? new Date(customer.ngay_tao).toLocaleString('vi-VN') : 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Người tạo">
            {customer?.nguoi_tao || 'N/A'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* Edit Modal */}
      <CustomerForm
        visible={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        onSuccess={handleEditSuccess}
        initialData={customer}
      />
    </>
  );
};

export default CustomerDetail;












