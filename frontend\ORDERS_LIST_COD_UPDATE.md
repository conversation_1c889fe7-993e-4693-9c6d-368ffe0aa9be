# 🔧 Cập Nhật OrdersList - Hi<PERSON><PERSON> Thị Tiền COD

## ✅ **<PERSON><PERSON><PERSON> cầu đã đáp ứng:**

### **1. ✅ Thêm cột "Tiền COD"**
- Hiển thị rõ ràng số tiền COD của từng đơn hàng
- <PERSON><PERSON><PERSON> cam (#fa8c16) để phân biệt với tổng tiền

### **2. ✅ Cột "Thanh toán" sử dụng tiền COD**
- Tính toán dựa trên: `remainingCOD = tien_cod - tong_da_tra`
- Hiển thị: "Còn COD: XXXđ" thay vì "Còn nợ: XXXđ"
- <PERSON><PERSON> "Cập nhật thanh toán" chỉ hiện khi còn COD chưa thanh toán

### **3. ✅ PaymentModal sử dụng tiền COD**
- <PERSON><PERSON> đư<PERSON> cập nhật trước đó để sử dụng `tien_cod` thay vì `tong_phai_tra`

## 🔧 **Chi tiết thay đổi:**

### **File: SaleSysFE/src/pages/Orders/OrdersList.jsx**

#### **1. Thêm cột "Tiền COD" (Line 319-330):**
```jsx
{
  title: "Tiền COD",
  dataIndex: "tien_cod",
  key: "tien_cod",
  width: 120,
  align: "right",
  render: (amount) => (
    <Text strong style={{ color: "#fa8c16" }}>
      {(amount || 0)?.toLocaleString("vi-VN")}đ
    </Text>
  ),
}
```

#### **2. Sửa logic cột "Thanh toán" (Line 337-360):**

**Trước (SAI):**
```jsx
const remainingAmount = record.con_phai_tra || 0;
const canPay = hasCustomer && confirmedStatuses.includes(record.trang_thai) && remainingAmount > 0;

console.log({
  tong_phai_tra: record.tong_phai_tra,
  con_phai_tra: record.con_phai_tra,
  remainingAmount,
  isFullyPaid: remainingAmount <= 0
});
```

**Sau (ĐÚNG):**
```jsx
// Sử dụng tiền COD để tính toán thanh toán
const codAmount = record.tien_cod || 0;
const paidAmount = record.tong_da_tra || 0;
const remainingCOD = codAmount - paidAmount;

const canPay = hasCustomer && confirmedStatuses.includes(record.trang_thai) && remainingCOD > 0;

console.log({
  tien_cod: record.tien_cod,
  tong_da_tra: record.tong_da_tra,
  remainingCOD,
  isFullyPaid: remainingCOD <= 0,
});
```

#### **3. Sửa hiển thị text (Line 366-382):**

**Trước:**
```jsx
{remainingAmount <= 0 ? "Đã thanh toán đủ" : `Còn nợ: ${remainingAmount.toLocaleString("vi-VN")}đ`}

{remainingAmount <= 0 ? (
  <Tag color="green">Đã thanh toán</Tag>
) : ...}
```

**Sau:**
```jsx
{remainingCOD <= 0 ? "Đã thanh toán đủ COD" : `Còn COD: ${remainingCOD.toLocaleString("vi-VN")}đ`}

{remainingCOD <= 0 ? (
  <Tag color="green">Đã thanh toán COD</Tag>
) : ...}
```

## 📊 **Ví dụ hiển thị:**

### **Table Layout mới:**
```
| Mã đơn | Khách hàng | Ngày bán | SP | Tổng tiền | Tiền COD | Thanh toán | Trạng thái |
|--------|------------|----------|----|-----------|-----------|-----------|-----------| 
| DH001  | Nguyễn A   | 15/01    | 3  | 1,000,000đ| 500,000đ | Còn COD:  | Đã xác nhận|
|        |            |          |    |           |           | 300,000đ  |           |
|        |            |          |    |           |           | [Cập nhật]|           |
```

### **Trường hợp cụ thể:**

#### **Case 1: Đơn hàng chưa thanh toán COD**
```
Dữ liệu:
- tong_tien: 1,000,000đ
- tien_cod: 500,000đ  
- tong_da_tra: 200,000đ

Hiển thị:
- Tổng tiền: 1,000,000đ (xanh dương)
- Tiền COD: 500,000đ (cam)
- Thanh toán: "Còn COD: 300,000đ" + [Cập nhật thanh toán]
```

#### **Case 2: Đã thanh toán đủ COD**
```
Dữ liệu:
- tong_tien: 1,000,000đ
- tien_cod: 500,000đ
- tong_da_tra: 500,000đ

Hiển thị:
- Tổng tiền: 1,000,000đ (xanh dương)
- Tiền COD: 500,000đ (cam)
- Thanh toán: "Đã thanh toán đủ COD" + [Đã thanh toán COD]
```

#### **Case 3: Đơn hàng không có COD**
```
Dữ liệu:
- tong_tien: 800,000đ
- tien_cod: 0đ
- tong_da_tra: 0đ

Hiển thị:
- Tổng tiền: 800,000đ (xanh dương)
- Tiền COD: 0đ (cam)
- Thanh toán: "Đã thanh toán đủ COD" + [Đã thanh toán COD]
```

## 🎯 **Logic mới:**

### **1. Tính toán thanh toán:**
```
remainingCOD = tien_cod - tong_da_tra
```

### **2. Điều kiện hiển thị button:**
```
canPay = hasCustomer && isConfirmed && remainingCOD > 0
```

### **3. Trạng thái thanh toán:**
- **remainingCOD > 0:** "Còn COD: XXXđ" + Button
- **remainingCOD <= 0:** "Đã thanh toán đủ COD" + Tag xanh

### **4. PaymentModal:**
- Sử dụng `tien_cod` làm max amount
- Hiển thị "Thanh toán COD: XXXđ"
- Validation theo tiền COD

## 🔄 **Workflow hoàn chỉnh:**

### **1. Tạo đơn hàng:**
```
Input: tong_tien, tien_cod, tien_coc
→ Hiển thị: Tổng tiền + Tiền COD riêng biệt
→ Thanh toán: "Còn COD: XXXđ"
```

### **2. Cập nhật thanh toán:**
```
Click [Cập nhật thanh toán]
→ PaymentModal mở với max = tien_cod
→ Submit payment
→ tong_da_tra += payment_amount
→ remainingCOD = tien_cod - tong_da_tra (updated)
```

### **3. Hoàn thành thanh toán:**
```
remainingCOD <= 0
→ Hiển thị: "Đã thanh toán đủ COD"
→ Button ẩn, Tag xanh hiện
```

## 🎨 **Color Scheme:**

- **Tổng tiền:** `#1890ff` (xanh dương) - Thông tin tham khảo
- **Tiền COD:** `#fa8c16` (cam) - Số tiền cần thu
- **Đã thanh toán:** `#52c41a` (xanh lá) - Hoàn thành
- **Còn COD:** `#666` (xám) - Thông tin phụ

## 🚀 **Lợi ích:**

### **1. Rõ ràng hơn:**
- Phân biệt rõ tổng tiền sản phẩm vs tiền COD
- Hiển thị chính xác số tiền cần thu
- Logic thanh toán đúng với business

### **2. UX tốt hơn:**
- Cột riêng cho tiền COD dễ nhìn
- Text "Còn COD" rõ ràng hơn "Còn nợ"
- Button chỉ hiện khi cần thiết

### **3. Consistent:**
- Đồng bộ với PaymentModal
- Đồng bộ với logic backend
- Đồng bộ với DebtManagement

## 🎉 **Hoàn thành:**

OrdersList đã được cập nhật để:
- ✅ **Hiển thị cột tiền COD** riêng biệt
- ✅ **Tính toán thanh toán theo COD** thay vì công nợ
- ✅ **Text và logic rõ ràng** cho COD
- ✅ **Đồng bộ với PaymentModal** đã cập nhật
- ✅ **Color scheme nhất quán**

Bây giờ trang Orders List sẽ hiển thị chính xác tiền COD và logic thanh toán! 🚀
