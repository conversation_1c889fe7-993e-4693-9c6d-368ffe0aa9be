'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ho_so_ca_nhan', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      nguoi_dung_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      ngay_sinh: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      gioi_tinh: {
        type: Sequelize.ENUM('nam', 'nu', 'khac'),
        allowNull: true
      },
      ma_so_thue: {
        type: Sequelize.STRING,
        allowNull: true
      },
      website: {
        type: Sequelize.STRING,
        allowNull: true
      },
      mo_ta: {
        type: Sequelize.TEXT,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('ho_so_ca_nhan', ['nguoi_dung_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ho_so_ca_nhan');
  }
};
