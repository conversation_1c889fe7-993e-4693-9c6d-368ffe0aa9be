# 🛒 Phần Mề<PERSON>ản Lý Bán Hàng - Frontend

Frontend React application cho hệ thống quản lý bán hàng.

## 🚀 Công nghệ sử dụng

- **React 18** - UI Framework
- **Vite** - Build tool
- **Ant Design** - UI Component Library
- **React Query** - Data fetching & caching
- **React Router DOM** - Routing
- **Recharts** - Charts & Graphs
- **Axios** - HTTP client

## 📦 Cài đặt

```bash
# Clone repository
git clone <repository-url>
cd SaleSysFE

# Cài đặt dependencies
npm install

# Copy environment file
cp .env.example .env

# Chỉnh sửa .env với cấu hình phù hợp
```

## 🔧 Cấu hình Environment

Tạo file `.env` từ `.env.example` và cấu hình:

```env
# For development
VITE_API_URL=http://localhost:5000/api

# For production
VITE_API_URL=https://your-backend-domain.com/api
```

## 🏃‍♂️ Chạy ứng dụng

```bash
# Development mode
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🌐 Deploy lên Vercel

### Bước 1: Chuẩn bị

1. Đảm bảo có file `vercel.json` (đã có sẵn)
2. Cấu hình environment variables trên Vercel:
   - `VITE_API_URL`: URL của backend API

### Bước 2: Deploy

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Hoặc deploy production
vercel --prod
```

### Bước 3: Cấu hình Domain (Optional)

Trên Vercel dashboard, có thể cấu hình custom domain.

## 🔧 Cấu hình quan trọng

### SPA Routing Fix

File `vercel.json` đã được cấu hình để xử lý client-side routing:

```json
{
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

Điều này giải quyết vấn đề 404 khi truy cập trực tiếp vào các route như `/dashboard`, `/products`, etc.

## 📁 Cấu trúc thư mục

```
src/
├── components/     # Reusable components
├── pages/         # Page components
├── services/      # API services
├── contexts/      # React contexts
├── hooks/         # Custom hooks
├── utils/         # Utility functions
└── config/        # Configuration files
```

## 🐛 Troubleshooting

### Lỗi 404 khi truy cập trực tiếp vào route

- Đảm bảo file `vercel.json` có mặt
- Kiểm tra cấu hình rewrites trong `vercel.json`

### API calls không hoạt động

- Kiểm tra `VITE_API_URL` trong environment variables
- Đảm bảo backend đang chạy và accessible
- Kiểm tra CORS configuration trên backend
