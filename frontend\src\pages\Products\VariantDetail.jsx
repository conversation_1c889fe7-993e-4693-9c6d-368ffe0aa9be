import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Card,
  Descriptions,
  Tag,
  Button,
  Space,
  Image,
  Table,
  Spin,
  Alert,
  Divider,
  Row,
  Col,
  Typography,
} from "antd";
import {
  EditOutlined,
  ArrowLeftOutlined,
  EyeOutlined,
  HistoryOutlined,
} from "@ant-design/icons";
import { useVariantStockHistory } from "../../hooks/useProducts";

const { Title } = Typography;

const VariantDetail = () => {
  const { productId, variantId } = useParams();
  const navigate = useNavigate();

  // Lấy thông tin phiên bản từ localStorage hoặc API
  const variant = JSON.parse(localStorage.getItem("selectedVariant") || "{}");
  const product = JSON.parse(localStorage.getItem("selectedProduct") || "{}");

  const {
    data: variantHistoryResponse,
    isLoading: historyLoading,
    error,
  } = useVariantStockHistory(variantId, { limit: 20 });

  if (!variant.id) {
    return (
      <Alert
        message="Không tìm thấy thông tin phiên bản"
        description="Vui lòng quay lại trang sản phẩm và chọn phiên bản"
        type="warning"
        showIcon
        style={{ margin: "20px" }}
        action={
          <Button onClick={() => navigate(`/products/${productId}`)}>
            Quay lại sản phẩm
          </Button>
        }
      />
    );
  }

  // Columns for stock history table
  const stockHistoryColumns = [
    {
      title: "Ngày thực hiện",
      dataIndex: "ngay_thuc_hien",
      key: "ngay_thuc_hien",
      width: 150,
      render: (date) => new Date(date).toLocaleString("vi-VN"),
    },
    {
      title: "Loại giao dịch",
      dataIndex: "loai",
      key: "loai",
      width: 120,
      render: (type) => {
        const typeMap = {
          nhap: { color: "green", text: "Nhập kho" },
          xuat: { color: "red", text: "Xuất kho" },
          dieu_chinh: { color: "blue", text: "Điều chỉnh" },
          chuyen_kho: { color: "orange", text: "Chuyển kho" },
        };
        const config = typeMap[type] || { color: "default", text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: "Kho",
      dataIndex: "ten_kho",
      key: "ten_kho",
      width: 120,
    },
    {
      title: "SL trước",
      dataIndex: "so_luong_truoc",
      key: "so_luong_truoc",
      width: 80,
      align: "right",
      render: (value) => value?.toLocaleString() || "0",
    },
    {
      title: "Thay đổi",
      dataIndex: "so_luong_thay_doi",
      key: "so_luong_thay_doi",
      width: 100,
      align: "right",
      render: (value) => {
        const color = value > 0 ? "green" : value < 0 ? "red" : "default";
        const prefix = value > 0 ? "+" : "";
        return (
          <span style={{ color }}>
            {prefix}
            {value?.toLocaleString() || "0"}
          </span>
        );
      },
    },
    {
      title: "SL sau",
      dataIndex: "so_luong_sau",
      key: "so_luong_sau",
      width: 80,
      align: "right",
      render: (value) => value?.toLocaleString() || "0",
    },
    {
      title: "Giá vốn",
      dataIndex: "gia_von",
      key: "gia_von",
      width: 120,
      align: "right",
      render: (value) =>
        value ? `${parseFloat(value).toLocaleString()} VNĐ` : "-",
    },
    {
      title: "Lý do",
      dataIndex: "ly_do",
      key: "ly_do",
      ellipsis: true,
    },
    {
      title: "Người thực hiện",
      dataIndex: "nguoi_thuc_hien",
      key: "nguoi_thuc_hien",
      width: 120,
    },
  ];

  // Extract stock history data
  const stockHistory = variantHistoryResponse?.data || [];

  return (
    <div style={{ padding: "24px" }}>
      {/* Header */}
      <div style={{ marginBottom: "24px" }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(`/products/${productId}`)}
          >
            Quay lại sản phẩm
          </Button>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => navigate(`/products/edit/${productId}`)}
          >
            Chỉnh sửa sản phẩm
          </Button>
        </Space>
      </div>

      <Title level={2}>Chi tiết phiên bản: {variant.ten_phien_ban}</Title>

      <Row gutter={[24, 24]}>
        {/* Variant Image */}
        <Col xs={24} md={8}>
          <Card title="Ảnh phiên bản" size="small">
            {variant.anh_phien_ban ||
            (product?.anhList && product.anhList.length > 0) ? (
              <Image
                src={variant.anh_phien_ban || product.anhList[0].url}
                alt={variant.ten_phien_ban}
                style={{
                  width: "100%",
                  borderRadius: 8,
                }}
              />
            ) : (
              <div
                style={{
                  textAlign: "center",
                  padding: "40px 20px",
                  color: "#999",
                }}
              >
                <EyeOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <p>Chưa có ảnh phiên bản</p>
              </div>
            )}
          </Card>
        </Col>

        {/* Variant Info */}
        <Col xs={24} md={16}>
          <Card title="Thông tin phiên bản" size="small">
            <Descriptions bordered column={2} size="small">
              <Descriptions.Item label="Tên phiên bản" span={2}>
                <strong>{variant.ten_phien_ban}</strong>
              </Descriptions.Item>

              <Descriptions.Item label="Mã SKU">
                {variant.ma || "-"}
              </Descriptions.Item>

              <Descriptions.Item label="Mã vạch">
                {variant.ma_vach || "-"}
              </Descriptions.Item>

              <Descriptions.Item label="Đơn vị tính">
                {variant.don_vi_tinh || "-"}
              </Descriptions.Item>

              <Descriptions.Item label="Khối lượng">
                {variant.khoi_luong || "-"}
              </Descriptions.Item>

              <Descriptions.Item label="Giá bán lẻ">
                <Tag color="green">
                  {variant.gia_le
                    ? `${variant.gia_le.toLocaleString()} VNĐ`
                    : "-"}
                </Tag>
              </Descriptions.Item>

              <Descriptions.Item label="Giá bán buôn">
                <Tag color="blue">
                  {variant.gia_buon
                    ? `${variant.gia_buon.toLocaleString()} VNĐ`
                    : "-"}
                </Tag>
              </Descriptions.Item>

              <Descriptions.Item label="Giá nhập">
                <Tag color="orange">
                  {variant.gia_nhap
                    ? `${variant.gia_nhap.toLocaleString()} VNĐ`
                    : "-"}
                </Tag>
              </Descriptions.Item>

              <Descriptions.Item label="Trạng thái">
                <Tag color={variant.trang_thai === 1 ? "green" : "red"}>
                  {variant.trang_thai === 1 ? "Hoạt động" : "Không hoạt động"}
                </Tag>
              </Descriptions.Item>

              <Descriptions.Item label="Mô tả" span={2}>
                {variant.mo_ta || "-"}
              </Descriptions.Item>

              <Descriptions.Item label="Ngày tạo">
                {variant.ngay_tao
                  ? new Date(variant.ngay_tao).toLocaleString("vi-VN")
                  : "-"}
              </Descriptions.Item>

              <Descriptions.Item label="Người tạo">
                {variant.nguoi_tao || "-"}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>

      {/* Variant Stock History */}
      <Divider />
      <Card
        title={
          <Space>
            <HistoryOutlined />
            <span>Lịch sử xuất nhập kho - {variant.ten_phien_ban}</span>
          </Space>
        }
        size="small"
      >
        {error ? (
          <Alert
            message="Lỗi tải lịch sử kho"
            description={error.message}
            type="error"
            showIcon
          />
        ) : (
          <Table
            columns={stockHistoryColumns}
            dataSource={stockHistory}
            rowKey="id"
            loading={historyLoading}
            pagination={{
              total: variantHistoryResponse?.pagination?.total || 0,
              pageSize: 20,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} của ${total} giao dịch`,
            }}
            size="small"
            scroll={{ x: 1200 }}
            locale={{
              emptyText:
                stockHistory.length === 0 && !historyLoading
                  ? "Chưa có lịch sử xuất nhập kho cho phiên bản này"
                  : undefined,
            }}
          />
        )}
      </Card>
    </div>
  );
};

export default VariantDetail;
