// API Configuration
const API_CONFIG = {
  // Base URL for API calls
  // BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  BASE_URL: import.meta.env.VITE_API_URL || "http://***************:5000/api",
  // API endpoints
  ENDPOINTS: {
    // Upload endpoints
    UPLOAD_SINGLE: "/upload/single",
    UPLOAD_MULTIPLE: "/upload/multiple",
    UPLOAD_PRODUCT: "/upload/product",
    DELETE_IMAGE: "/upload",

    // Product endpoints
    PRODUCTS: "/products",
    PRODUCT_CATEGORIES: "/product-categories",

    // Auth endpoints
    LOGIN: "/auth/login",
    REGISTER: "/auth/register",

    // Other endpoints
    WAREHOUSES: "/warehouses",
    CUSTOMERS: "/customers",
    ORDERS: "/orders",
  },
};

// Helper function to build full URL
export const buildApiUrl = (endpoint) => {
  // If proxy is configured, use relative URL
  if (import.meta.env.DEV && window.location.hostname === "localhost") {
    return endpoint; // Use proxy
  }

  // Otherwise use full URL
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// API helper functions
export const apiCall = async (endpoint, options = {}) => {
  const url = buildApiUrl(endpoint);

  const defaultOptions = {
    headers: {
      "Content-Type": "application/json",
    },
  };

  // Don't set Content-Type for FormData
  if (options.body instanceof FormData) {
    delete defaultOptions.headers["Content-Type"];
  }

  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    console.log(`🌐 API Call: ${options.method || "GET"} ${url}`);

    const response = await fetch(url, config);

    console.log(`📡 Response: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`❌ API Error for ${url}:`, error);
    throw error;
  }
};

// Upload specific helper
export const uploadImage = async (file, options = {}) => {
  const formData = new FormData();
  formData.append("image", file);

  // Add optional parameters
  if (options.folder) formData.append("folder", options.folder);
  if (options.width) formData.append("width", options.width.toString());
  if (options.height) formData.append("height", options.height.toString());

  return apiCall(API_CONFIG.ENDPOINTS.UPLOAD_SINGLE, {
    method: "POST",
    body: formData,
  });
};

export default API_CONFIG;
