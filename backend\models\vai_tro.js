'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class VaiTro extends Model {
    static associate(models) {
      // Quan hệ với người dùng (n-n)
      VaiTro.belongsToMany(models.NguoiDung, {
        through: models.NguoiDungVaiTro,
        foreignKey: 'vai_tro_id',
        otherKey: 'nguoi_dung_id',
        as: 'nguoiDungList'
      });

      // Quan hệ với quyền (n-n)
      VaiTro.belongsToMany(models.Quyen, {
        through: models.VaiTroQuyen,
        foreignKey: 'vai_tro_id',
        otherKey: 'quyen_id',
        as: 'quyenList'
      });
    }
  }

  VaiTro.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ma_vai_tro: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        isUppercase: true
      }
    },
    ten_vai_tro: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    mo_ta: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'VaiTro',
    tableName: 'vai_tro',
    timestamps: false
  });

  return VaiTro;
};
