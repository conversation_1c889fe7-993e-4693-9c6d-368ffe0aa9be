'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('quyen', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      ma_quyen: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      ten_quyen: {
        type: Sequelize.STRING,
        allowNull: false
      },
      mo_ta: {
        type: Sequelize.TEXT,
        allowNull: true
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('quyen');
  }
};
