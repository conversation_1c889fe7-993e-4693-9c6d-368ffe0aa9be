'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('don_hang_dia_chi_giao', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      don_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'don_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      dia_chi: {
        type: Sequelize.STRING,
        allowNull: false
      },
      phuong_xa: {
        type: Sequelize.STRING,
        allowNull: true
      },
      quan_huyen: {
        type: Sequelize.STRING,
        allowNull: true
      },
      tinh_thanh: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_nhan: {
        type: Sequelize.STRING,
        allowNull: true
      },
      so_dien_thoai_nhan: {
        type: Sequelize.STRING,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('don_hang_dia_chi_giao', ['don_hang_id']);
    await queryInterface.addIndex('don_hang_dia_chi_giao', ['tinh_thanh']);
    await queryInterface.addIndex('don_hang_dia_chi_giao', ['quan_huyen']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('don_hang_dia_chi_giao');
  }
};
