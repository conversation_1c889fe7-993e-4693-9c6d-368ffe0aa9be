const axios = require('axios');

async function testManualTotal() {
  const baseURL = 'http://localhost:5000/api';
  
  try {
    console.log('🧪 Testing Manual Total Feature...\n');

    // Test data
    const testOrders = [
      {
        name: '<PERSON><PERSON><PERSON> hàng tự động (bình thường)',
        data: {
          khach_hang_id: 20,
          ten_khach_hang: 'Nguyễn Việt Anh',
          so_dien_thoai: '0123456789',
          dia_chi: 'Test Address',
          ngay_dat_hang: new Date().toISOString(),
          trang_thai: 'cho_xu_ly',
          ghi_chu: 'Đơn hàng test tự động',
          chi_tiet_don_hang: [
            {
              phien_ban_san_pham_id: 1,
              so_luong: 2,
              gia_ban: 100000,
              thanh_tien: 200000
            }
          ],
          tong_tien_hang: 200000,
          giam_gia: 0,
          tong_thanh_toan: 200000,
          is_manual_total: false
        }
      },
      {
        name: '<PERSON><PERSON><PERSON> hàng thủ công (gi<PERSON>m gi<PERSON> đặc biệt)',
        data: {
          khach_hang_id: 20,
          ten_khach_hang: '<PERSON>uy<PERSON><PERSON>iệt Anh',
          so_dien_thoai: '0123456789',
          dia_chi: 'Test Address',
          ngay_dat_hang: new Date().toISOString(),
          trang_thai: 'cho_xu_ly',
          ghi_chu: 'Khách VIP, giảm giá đặc biệt',
          chi_tiet_don_hang: [
            {
              phien_ban_san_pham_id: 1,
              so_luong: 3,
              gia_ban: 150000,
              thanh_tien: 450000
            }
          ],
          tong_tien_hang: 450000,
          giam_gia: 0,
          tong_thanh_toan: 350000, // Giảm 100k cho khách VIP
          is_manual_total: true
        }
      },
      {
        name: 'Đơn hàng thủ công (khách đã trả trước)',
        data: {
          khach_hang_id: 20,
          ten_khach_hang: 'Nguyễn Việt Anh',
          so_dien_thoai: '0123456789',
          dia_chi: 'Test Address',
          ngay_dat_hang: new Date().toISOString(),
          trang_thai: 'cho_xu_ly',
          ghi_chu: 'Khách đã đặt cọc 500k trước',
          chi_tiet_don_hang: [
            {
              phien_ban_san_pham_id: 1,
              so_luong: 10,
              gia_ban: 100000,
              thanh_tien: 1000000
            }
          ],
          tong_tien_hang: 1000000,
          giam_gia: 0,
          tong_thanh_toan: 500000, // Còn lại 500k sau khi trừ cọc
          is_manual_total: true
        }
      }
    ];

    const createdOrders = [];

    // Test tạo các đơn hàng
    for (const testOrder of testOrders) {
      console.log(`📋 Testing: ${testOrder.name}`);
      
      try {
        const response = await axios.post(`${baseURL}/orders`, testOrder.data, {
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json'
          }
        });

        if (response.data.success) {
          const order = response.data.data;
          createdOrders.push(order);
          
          console.log(`✅ Created order: ${order.ma_don_hang}`);
          console.log(`   - Tổng tiền hàng: ${testOrder.data.tong_tien_hang.toLocaleString('vi-VN')}đ`);
          console.log(`   - Tổng phải trả: ${order.tong_phai_tra.toLocaleString('vi-VN')}đ`);
          console.log(`   - Manual total: ${testOrder.data.is_manual_total ? 'Yes' : 'No'}`);
          console.log(`   - Ghi chú: ${order.ghi_chu}`);
        } else {
          console.log(`❌ Failed to create order: ${response.data.message}`);
        }
      } catch (error) {
        console.log(`❌ Error creating order: ${error.response?.data?.message || error.message}`);
      }
      
      console.log('');
    }

    // Test lấy chi tiết đơn hàng
    console.log('📋 Testing order details...\n');
    for (const order of createdOrders) {
      try {
        const detailResponse = await axios.get(`${baseURL}/orders/${order.id}`, {
          headers: { 'Authorization': 'Bearer test-token' }
        });

        if (detailResponse.data.success) {
          const orderDetail = detailResponse.data.data;
          console.log(`📄 Order ${orderDetail.ma_don_hang} details:`);
          console.log(`   - Tổng tiền: ${orderDetail.tong_tien?.toLocaleString('vi-VN')}đ`);
          console.log(`   - Chiết khấu: ${orderDetail.chiet_khau?.toLocaleString('vi-VN')}đ`);
          console.log(`   - Tổng phải trả: ${orderDetail.tong_phai_tra?.toLocaleString('vi-VN')}đ`);
          console.log(`   - Còn phải trả: ${orderDetail.con_phai_tra?.toLocaleString('vi-VN')}đ`);
          console.log(`   - Ghi chú: ${orderDetail.ghi_chu}`);
        }
      } catch (error) {
        console.log(`❌ Error getting order details: ${error.response?.data?.message || error.message}`);
      }
      console.log('');
    }

    console.log('🎉 Manual total test completed!');
    console.log('\n📊 Test Summary:');
    console.log(`   - Total orders created: ${createdOrders.length}`);
    console.log(`   - Manual total orders: ${testOrders.filter(o => o.data.is_manual_total).length}`);
    console.log(`   - Auto total orders: ${testOrders.filter(o => !o.data.is_manual_total).length}`);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      console.log('\n💡 Note: You need to be authenticated to run this test.');
      console.log('   Try running this test through the frontend or with a valid token.');
    }
  }
}

// Run the test
testManualTotal();
