const { exec } = require('child_process');
const path = require('path');

console.log('🔐 Adding missing permissions to database...');
console.log('');

// Chạy seeder để thêm các quyền còn thiếu
exec(
  'npx sequelize-cli db:seed --seed 20250130000001-add-missing-permissions.js',
  { cwd: __dirname },
  (error, stdout, stderr) => {
    if (error) {
      console.error('❌ Error running seeder:', error);
      return;
    }
    
    if (stderr) {
      console.error('⚠️ Seeder warnings:', stderr);
    }
    
    console.log('📋 Seeder output:', stdout);
    console.log('✅ Missing permissions added successfully!');
    console.log('');
    console.log('📝 Added permissions:');
    console.log('  📦 Product Categories:');
    console.log('    - XEM_LOAI_SAN_PHAM: Xem loại sản phẩm');
    console.log('    - THEM_LOAI_SAN_PHAM: Thêm loại sản phẩm');
    console.log('    - SUA_LOAI_SAN_PHAM: Sửa loại sản phẩm');
    console.log('    - XOA_LOAI_SAN_PHAM: Xóa loại sản phẩm');
    console.log('');
    console.log('  🏷️ Brands:');
    console.log('    - XEM_NHAN_HIEU: Xem nhãn hiệu');
    console.log('    - THEM_NHAN_HIEU: Thêm nhãn hiệu');
    console.log('    - SUA_NHAN_HIEU: Sửa nhãn hiệu');
    console.log('    - XOA_NHAN_HIEU: Xóa nhãn hiệu');
    console.log('');
    console.log('  👥 Customer Groups:');
    console.log('    - XEM_NHOM_KHACH_HANG: Xem nhóm khách hàng');
    console.log('    - THEM_NHOM_KHACH_HANG: Thêm nhóm khách hàng');
    console.log('    - SUA_NHOM_KHACH_HANG: Sửa nhóm khách hàng');
    console.log('    - XOA_NHOM_KHACH_HANG: Xóa nhóm khách hàng');
    console.log('');
    console.log('  🏷️ Tags:');
    console.log('    - XEM_TAG: Xem tag');
    console.log('    - THEM_TAG: Thêm tag');
    console.log('    - SUA_TAG: Sửa tag');
    console.log('    - XOA_TAG: Xóa tag');
    console.log('');
    console.log('  📋 Stock Check:');
    console.log('    - XEM_KIEM_KE: Xem kiểm kê');
    console.log('    - THEM_KIEM_KE: Thêm kiểm kê');
    console.log('    - SUA_KIEM_KE: Sửa kiểm kê');
    console.log('    - XOA_KIEM_KE: Xóa kiểm kê');
    console.log('');
    console.log('👤 Assigned to roles:');
    console.log('  - ADMIN: All new permissions');
    console.log('  - QUAN_LY: All new permissions except DELETE permissions');
    console.log('');
    console.log('🔄 Please restart your frontend to see updated permissions!');
  }
);
