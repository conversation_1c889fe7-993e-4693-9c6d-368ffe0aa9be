'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('phien_ban_gia_tri_thuoc_tinh', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      phien_ban_san_pham_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'phien_ban_san_pham',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      gia_tri_thuoc_tinh_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'gia_tri_thuoc_tinh',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('phien_ban_gia_tri_thuoc_tinh', ['phien_ban_san_pham_id']);
    await queryInterface.addIndex('phien_ban_gia_tri_thuoc_tinh', ['gia_tri_thuoc_tinh_id']);
    
    // Add unique constraint for combination
    await queryInterface.addIndex('phien_ban_gia_tri_thuoc_tinh', ['phien_ban_san_pham_id', 'gia_tri_thuoc_tinh_id'], {
      unique: true,
      name: 'unique_phien_ban_gia_tri_thuoc_tinh'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('phien_ban_gia_tri_thuoc_tinh');
  }
};
