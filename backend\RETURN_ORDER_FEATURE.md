# 🔄 Chức Năng Hoàn Hàng

## 🎯 Mục Tiêu
Thêm chức năng hoàn hàng với logic tự động cập nhật tồn kho và công nợ khách hàng.

## ✅ Những Gì Đã Hoàn Thành

### 1. **Backend Implementation**

#### **Database Schema**
- ✅ **Thêm trạng thái `hoan_hang`** vào ENUM của bảng `don_hang`
- ✅ **Migration script** để cập nhật database schema

#### **API Endpoint**
- ✅ **POST `/api/orders/:id/return`** - API hoàn hàng
- ✅ **Logic hoàn tồn kho** tự động
- ✅ **Logic điều chỉnh công nợ** tự động
- ✅ **Validation** trạng thái đơn hàng
- ✅ **Transaction handling** đảm bảo data consistency

#### **Controller Logic**
```javascript
// <PERSON><PERSON><PERSON> bước xử lý hoàn hàng:
1. <PERSON><PERSON>m tra trạng thái đơn hàng (chỉ cho phép 'da_giao', 'hoan_thanh')
2. Hoàn tồn kho cho từng sản phẩm
3. Điều chỉnh công nợ khách hàng (nếu có)
4. Cập nhật trạng thái đơn hàng thành 'hoan_hang'
5. Ghi log lý do hoàn hàng vào ghi chú
```

### 2. **Frontend Implementation**

#### **UI Components**
- ✅ **Button "Hoàn hàng"** trong OrderDetail
- ✅ **Modal xác nhận hoàn hàng** với form input
- ✅ **Validation** lý do hoàn hàng bắt buộc
- ✅ **Loading states** và error handling

#### **Constants & Status**
- ✅ **Thêm `hoan_hang`** vào ORDER_STATUS constants
- ✅ **Label và color** cho trạng thái hoàn hàng
- ✅ **Logic hiển thị button** dựa trên trạng thái

#### **Hooks & API**
- ✅ **useReturnOrder hook** với React Query
- ✅ **API integration** với error handling
- ✅ **Auto refresh** data sau khi hoàn hàng

### 3. **Business Logic**

#### **Điều Kiện Hoàn Hàng**
- ✅ Chỉ cho phép hoàn hàng đơn có trạng thái: `da_giao`, `hoan_thanh`
- ✅ Yêu cầu nhập lý do hoàn hàng
- ✅ Ghi chú thêm (optional)

#### **Tự Động Cập Nhật Tồn Kho**
```javascript
// Logic hoàn tồn kho:
- Tìm hoặc tạo record TonKhoPhienBan
- Cộng lại số lượng đã bán vào tồn kho
- Cập nhật so_luong_ton và so_luong_co_the_ban
```

#### **Tự Động Điều Chỉnh Công Nợ**
```javascript
// Logic điều chỉnh công nợ:
- Kiểm tra khách hàng có công nợ không
- Trừ số tiền đơn hàng khỏi tổng công nợ
- Cập nhật bảng CongNoNguoiDung
```

## 🚀 Cách Sử Dụng

### **1. Từ Frontend (Khuyến nghị)**

#### **Bước 1: Mở chi tiết đơn hàng**
- Vào trang Đơn hàng → Click vào đơn hàng cần hoàn
- Hoặc truy cập trực tiếp: `/orders/{order_id}`

#### **Bước 2: Kiểm tra điều kiện**
- Đơn hàng phải có trạng thái: **"Đã giao"** hoặc **"Hoàn thành"**
- Button **"Hoàn hàng"** sẽ hiển thị màu đỏ

#### **Bước 3: Thực hiện hoàn hàng**
1. Click button **"Hoàn hàng"**
2. Nhập **lý do hoàn hàng** (bắt buộc)
3. Nhập **ghi chú thêm** (tùy chọn)
4. Click **"Xác nhận hoàn hàng"**

#### **Bước 4: Kiểm tra kết quả**
- Trạng thái đơn hàng chuyển thành **"Hoàn hàng"**
- Tồn kho được cập nhật tự động
- Công nợ khách hàng được điều chỉnh

### **2. Từ API (Cho developers)**

#### **Endpoint**
```http
POST /api/orders/:id/return
Authorization: Bearer {token}
Content-Type: application/json

{
  "ly_do_hoan_hang": "Sản phẩm bị lỗi",
  "ghi_chu_hoan_hang": "Khách hàng phản ánh không đúng mô tả"
}
```

#### **Response**
```json
{
  "success": true,
  "message": "Hoàn hàng thành công",
  "data": {
    "order_id": 123,
    "old_status": "da_giao",
    "new_status": "hoan_hang",
    "returned_products": 2,
    "debt_adjustment": 500000,
    "ly_do_hoan_hang": "Sản phẩm bị lỗi",
    "ghi_chu_hoan_hang": "Khách hàng phản ánh không đúng mô tả"
  }
}
```

## 🧪 Testing

### **1. Test Script**
```bash
cd SaleSysBE
node test-return-order.js
```

### **2. Manual Testing Steps**
1. **Tạo đơn hàng test** với sản phẩm có tồn kho
2. **Cập nhật trạng thái** thành "Đã giao"
3. **Ghi nhận tồn kho** trước khi hoàn hàng
4. **Thực hiện hoàn hàng** qua Frontend hoặc API
5. **Kiểm tra kết quả**:
   - Trạng thái đơn hàng = `hoan_hang`
   - Tồn kho tăng lên
   - Công nợ giảm xuống

### **3. Test Cases**
- ✅ Hoàn hàng đơn "Đã giao" → Thành công
- ✅ Hoàn hàng đơn "Hoàn thành" → Thành công  
- ❌ Hoàn hàng đơn "Chờ xử lý" → Lỗi validation
- ❌ Hoàn hàng không có lý do → Lỗi validation
- ✅ Tồn kho được cập nhật đúng
- ✅ Công nợ được điều chỉnh đúng

## 📊 Database Changes

### **Schema Update**
```sql
-- Trước
ENUM('cho_xu_ly', 'da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'huy')

-- Sau  
ENUM('cho_xu_ly', 'da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'huy', 'hoan_hang')
```

### **Migration Command**
```bash
# Đã chạy tự động
node add-hoan-hang-status.js
```

## 🎯 Kết Quả

✅ **Chức năng hoàn hàng hoàn chỉnh**
✅ **Tự động cập nhật tồn kho**
✅ **Tự động điều chỉnh công nợ**
✅ **UI/UX thân thiện**
✅ **Error handling tốt**
✅ **Transaction safety**
✅ **Logging đầy đủ**

## 🔮 Tính Năng Mở Rộng

### **Có Thể Thêm Sau**
- **Hoàn hàng một phần** (chỉ hoàn một số sản phẩm)
- **Lý do hoàn hàng dropdown** với các lựa chọn có sẵn
- **Upload hình ảnh** sản phẩm hoàn hàng
- **Workflow approval** cho hoàn hàng giá trị cao
- **Email notification** cho khách hàng
- **Báo cáo hoàn hàng** theo thời gian

Chức năng hoàn hàng đã sẵn sàng sử dụng trong production! 🎉
