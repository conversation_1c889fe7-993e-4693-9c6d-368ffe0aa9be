'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Insert products
    await queryInterface.bulkInsert('san_pham', [
      {
        ten: '<PERSON><PERSON> thun Nike Dri-FIT',
        ma: 'AT001',
        loai_san_pham_id: 1, // <PERSON>o thun
        nhan_hieu_id: 1, // Nike
        tag_id: 1, // Mới nhất
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Quần jean Adidas Originals',
        ma: 'QJ001',
        loai_san_pham_id: 2, // Quần jean
        nhan_hieu_id: 2, // Adidas
        tag_id: 2, // Bán chạy
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: '<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>iqlo Heattech',
        ma: 'AK001',
        loai_san_pham_id: 5, // <PERSON><PERSON> kho<PERSON><PERSON>
        nhan_hieu_id: 3, // Uniqlo
        tag_id: 3, // Giảm giá
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Giày thể thao Nike Air Max',
        ma: 'GT001',
        loai_san_pham_id: 3, // Giày dép
        nhan_hieu_id: 1, // Nike
        tag_id: 4, // Cao cấp
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Túi xách Zara Classic',
        ma: 'TX001',
        loai_san_pham_id: 4, // Phụ kiện
        nhan_hieu_id: 4, // Zara
        tag_id: 5, // Phổ biến
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});

    // Insert product variants
    await queryInterface.bulkInsert('phien_ban_san_pham', [
      // Áo thun Nike - Size S, M, L
      {
        san_pham_id: 1,
        ten_phien_ban: 'Size S - Đỏ',
        ma: 'AT001-S-RED',
        gia_le: 299000,
        gia_buon: 250000,
        gia_nhap: 200000,
        don_vi_tinh: 'Cái',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        san_pham_id: 1,
        ten_phien_ban: 'Size M - Đỏ',
        ma: 'AT001-M-RED',
        gia_le: 299000,
        gia_buon: 250000,
        gia_nhap: 200000,
        don_vi_tinh: 'Cái',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        san_pham_id: 1,
        ten_phien_ban: 'Size L - Đỏ',
        ma: 'AT001-L-RED',
        gia_le: 299000,
        gia_buon: 250000,
        gia_nhap: 200000,
        don_vi_tinh: 'Cái',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      // Quần jean Adidas - Size 30, 32, 34
      {
        san_pham_id: 2,
        ten_phien_ban: 'Size 30 - Xanh đen',
        ma: 'QJ001-30-BLUE',
        gia_le: 899000,
        gia_buon: 750000,
        gia_nhap: 600000,
        don_vi_tinh: 'Cái',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        san_pham_id: 2,
        ten_phien_ban: 'Size 32 - Xanh đen',
        ma: 'QJ001-32-BLUE',
        gia_le: 899000,
        gia_buon: 750000,
        gia_nhap: 600000,
        don_vi_tinh: 'Cái',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      // Áo khoác Uniqlo - Size M, L
      {
        san_pham_id: 3,
        ten_phien_ban: 'Size M - Đen',
        ma: 'AK001-M-BLACK',
        gia_le: 1299000,
        gia_buon: 1100000,
        gia_nhap: 900000,
        don_vi_tinh: 'Cái',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      // Giày Nike - Size 40, 41, 42
      {
        san_pham_id: 4,
        ten_phien_ban: 'Size 40 - Trắng',
        ma: 'GT001-40-WHITE',
        gia_le: 2499000,
        gia_buon: 2100000,
        gia_nhap: 1800000,
        don_vi_tinh: 'Đôi',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        san_pham_id: 4,
        ten_phien_ban: 'Size 41 - Trắng',
        ma: 'GT001-41-WHITE',
        gia_le: 2499000,
        gia_buon: 2100000,
        gia_nhap: 1800000,
        don_vi_tinh: 'Đôi',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      // Túi xách Zara
      {
        san_pham_id: 5,
        ten_phien_ban: 'Màu nâu - Size M',
        ma: 'TX001-M-BROWN',
        gia_le: 1599000,
        gia_buon: 1350000,
        gia_nhap: 1100000,
        don_vi_tinh: 'Cái',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('phien_ban_san_pham', null, {});
    await queryInterface.bulkDelete('san_pham', null, {});
  }
};
