import { useState } from 'react';
import {
  Input,
  Button,
  Space,
  Row,
  Col,
  Select,
  DatePicker,
  Typography
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;

const SimpleSearchFilter = ({
  // Search props
  searchPlaceholder = "Tìm kiếm...",
  onSearch,
  showSearch = true,
  
  // Filter props
  filters = [],
  onFilter,
  onReset,
  
  // Initial values
  initialValues = {},
  
  // Style props
  className,
  style
}) => {
  const [searchValue, setSearchValue] = useState(initialValues.search || '');
  const [filterValues, setFilterValues] = useState(initialValues);

  // Handle search
  const handleSearch = (value) => {
    setSearchValue(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  // Handle filter change
  const handleFilterChange = (key, value) => {
    const newValues = { ...filterValues, [key]: value };
    setFilterValues(newValues);
    if (onFilter) {
      onFilter(newValues);
    }
  };

  // Handle reset
  const handleReset = () => {
    setSearchValue('');
    setFilterValues({});
    if (onReset) {
      onReset();
    }
  };

  // Render filter input based on type
  const renderFilterInput = (filter) => {
    switch (filter.type) {
      case 'select':
        return (
          <Select
            key={filter.key}
            placeholder={filter.placeholder}
            style={{ width: '100%' }}
            allowClear
            value={filterValues[filter.key]}
            onChange={(value) => handleFilterChange(filter.key, value)}
          >
            {filter.options?.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
      
      case 'dateRange':
        return (
          <RangePicker
            key={filter.key}
            placeholder={filter.placeholder}
            style={{ width: '100%' }}
            value={filterValues[filter.key]}
            onChange={(value) => handleFilterChange(filter.key, value)}
          />
        );
      
      case 'date':
        return (
          <DatePicker
            key={filter.key}
            placeholder={filter.placeholder}
            style={{ width: '100%' }}
            value={filterValues[filter.key]}
            onChange={(value) => handleFilterChange(filter.key, value)}
          />
        );
      
      default:
        return (
          <Input
            key={filter.key}
            placeholder={filter.placeholder}
            value={filterValues[filter.key]}
            onChange={(e) => handleFilterChange(filter.key, e.target.value)}
          />
        );
    }
  };

  return (
    <div className={className} style={style}>
      <Row gutter={[16, 16]} align="middle">
        {/* Search */}
        {showSearch && (
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input
              placeholder={searchPlaceholder}
              prefix={<SearchOutlined />}
              value={searchValue}
              onChange={(e) => handleSearch(e.target.value)}
              allowClear
            />
          </Col>
        )}
        
        {/* Filters */}
        {filters.map((filter) => (
          <Col key={filter.key} xs={24} sm={12} md={8} lg={6}>
            <div>
              <div style={{ marginBottom: 4, fontSize: 12, color: '#666' }}>
                {filter.label}
              </div>
              {renderFilterInput(filter)}
            </div>
          </Col>
        ))}
        
        {/* Actions */}
        <Col>
          <Space>
            <Button 
              icon={<FilterOutlined />}
              onClick={() => onFilter && onFilter(filterValues)}
            >
              Lọc
            </Button>
            <Button 
              icon={<ReloadOutlined />}
              onClick={handleReset}
            >
              Đặt lại
            </Button>
          </Space>
        </Col>
      </Row>
    </div>
  );
};

export default SimpleSearchFilter;
