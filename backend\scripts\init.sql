-- Initialize database for SellPro application
-- This script runs when the MySQL container starts for the first time

-- Set timezone
SET time_zone = '+07:00';

-- Set character set and collation
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create database if not exists (already handled by MYSQL_DATABASE env var)
-- CREATE DATABASE IF NOT EXISTS sellpro_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user if not exists (already handled by MYSQL_USER env var)
-- CREATE USER IF NOT EXISTS 'sellpro_user'@'%' IDENTIFIED BY 'password';
-- GRANT ALL PRIVILEGES ON sellpro_db.* TO 'sellpro_user'@'%';

-- Set some MySQL optimizations for the application
SET GLOBAL innodb_buffer_pool_size = 268435456; -- 256MB
SET GLOBAL max_connections = 100;
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;

-- <PERSON><PERSON>h privileges
FLUSH PRIVILEGES;

-- Log initialization
SELECT 'SellPro database initialized successfully' AS message;
