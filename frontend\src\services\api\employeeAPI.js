import api from '../api';

const employeeAPI = {
  getEmployees: (params = {}) => {
    return api.get('/employees', { params });
  },
  
  getEmployee: (id) => {
    return api.get(`/employees/${id}`);
  },
  
  createEmployee: (data) => {
    return api.post('/employees', data);
  },
  
  updateEmployee: (id, data) => {
    return api.put(`/employees/${id}`, data);
  },
  
  deleteEmployee: (id) => {
    return api.delete(`/employees/${id}`);
  },
  
  assignCustomers: (id, customerIds) => {
    return api.post(`/employees/${id}/assign-customers`, { khach_hang_ids: customerIds });
  },
  
  getAssignedCustomers: (id, params = {}) => {
    return api.get(`/employees/${id}/assigned-customers`, { params });
  }
};

export default employeeAPI;

