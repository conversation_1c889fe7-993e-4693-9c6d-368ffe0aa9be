'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('don_hang_tag', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      don_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'don_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      tag_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'tag',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      }
    });

    // Add indexes
    await queryInterface.addIndex('don_hang_tag', ['don_hang_id'], {
      name: 'idx_don_hang_tag_don_hang_id'
    });
    await queryInterface.addIndex('don_hang_tag', ['tag_id'], {
      name: 'idx_don_hang_tag_tag_id'
    });

    // Add unique constraint for combination
    await queryInterface.addIndex('don_hang_tag', ['don_hang_id', 'tag_id'], {
      unique: true,
      name: 'unique_don_hang_tag_combination'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('don_hang_tag');
  }
};
