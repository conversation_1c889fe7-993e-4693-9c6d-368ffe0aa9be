const express = require('express');
const jwt = require('jsonwebtoken');
const { NguoiDung, VaiTro, Quyen } = require('../models');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

/**
 * @route POST /api/auth/login
 * @desc Đăng nhập
 * @access Public
 */
router.post('/login', asyncHandler(async (req, res) => {
  const { email, mat_khau } = req.body;

  // Validation
  if (!email || !mat_khau) {
    throw new AppError('Email và mật khẩu là bắt buộc', 400);
  }

  // Tìm user theo email
  const user = await NguoiDung.findOne({
    where: { email },
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList',
        include: [
          {
            model: <PERSON>uy<PERSON>,
            as: 'quyenList'
          }
        ]
      }
    ]
  });

  if (!user) {
    throw new AppError('Email hoặc mật khẩu không đúng', 401);
  }

  // Kiểm tra mật khẩu
  const isValidPassword = await user.checkPassword(mat_khau);
  if (!isValidPassword) {
    throw new AppError('Email hoặc mật khẩu không đúng', 401);
  }

  // Kiểm tra trạng thái tài khoản
  if (user.trang_thai !== 'dang_giao_dich') {
    throw new AppError('Tài khoản đã bị khóa', 401);
  }

  // Tạo JWT token
  const token = jwt.sign(
    { 
      userId: user.id,
      email: user.email,
      loai_nguoi_dung: user.loai_nguoi_dung
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );

  // Tạo danh sách quyền
  const permissions = [];
  user.vaiTroList?.forEach(role => {
    role.quyenList?.forEach(permission => {
      if (!permissions.includes(permission.ma_quyen)) {
        permissions.push(permission.ma_quyen);
      }
    });
  });

  res.json({
    success: true,
    message: 'Đăng nhập thành công',
    data: {
      token,
      user: {
        id: user.id,
        ho_ten: user.ho_ten,
        email: user.email,
        so_dien_thoai: user.so_dien_thoai,
        loai_nguoi_dung: user.loai_nguoi_dung,
        trang_thai: user.trang_thai,
        vaiTroList: user.vaiTroList,
        permissions
      }
    }
  });
}));

/**
 * @route POST /api/auth/register
 * @desc Đăng ký tài khoản mới
 * @access Public
 */
router.post('/register', asyncHandler(async (req, res) => {
  const { ho_ten, email, mat_khau, so_dien_thoai, loai_nguoi_dung = 'khach_hang' } = req.body;

  // Validation
  if (!ho_ten || !email || !mat_khau) {
    throw new AppError('Họ tên, email và mật khẩu là bắt buộc', 400);
  }

  // Kiểm tra email đã tồn tại
  const existingUser = await NguoiDung.findOne({ where: { email } });
  if (existingUser) {
    throw new AppError('Email đã được sử dụng', 400);
  }

  // Tạo user mới
  const newUser = await NguoiDung.create({
    ho_ten,
    email,
    mat_khau,
    so_dien_thoai,
    loai_nguoi_dung,
    nguoi_tao: 'system'
  });

  res.status(201).json({
    success: true,
    message: 'Đăng ký thành công',
    data: {
      user: {
        id: newUser.id,
        ho_ten: newUser.ho_ten,
        email: newUser.email,
        so_dien_thoai: newUser.so_dien_thoai,
        loai_nguoi_dung: newUser.loai_nguoi_dung,
        trang_thai: newUser.trang_thai
      }
    }
  });
}));

/**
 * @route GET /api/auth/me
 * @desc Lấy thông tin user hiện tại
 * @access Private
 */
router.get('/me', authenticateToken, asyncHandler(async (req, res) => {
  const user = await NguoiDung.findByPk(req.userId, {
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList',
        include: [
          {
            model: Quyen,
            as: 'quyenList'
          }
        ]
      }
    ]
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Tạo danh sách quyền
  const permissions = [];
  user.vaiTroList?.forEach(role => {
    role.quyenList?.forEach(permission => {
      if (!permissions.includes(permission.ma_quyen)) {
        permissions.push(permission.ma_quyen);
      }
    });
  });

  res.json({
    success: true,
    data: {
      user: {
        id: user.id,
        ho_ten: user.ho_ten,
        email: user.email,
        so_dien_thoai: user.so_dien_thoai,
        loai_nguoi_dung: user.loai_nguoi_dung,
        trang_thai: user.trang_thai,
        vaiTroList: user.vaiTroList,
        permissions
      }
    }
  });
}));

/**
 * @route POST /api/auth/change-password
 * @desc Đổi mật khẩu
 * @access Private
 */
router.post('/change-password', authenticateToken, asyncHandler(async (req, res) => {
  const { mat_khau_cu, mat_khau_moi } = req.body;

  if (!mat_khau_cu || !mat_khau_moi) {
    throw new AppError('Mật khẩu cũ và mật khẩu mới là bắt buộc', 400);
  }

  const user = await NguoiDung.findByPk(req.userId);
  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Kiểm tra mật khẩu cũ
  const isValidPassword = await user.checkPassword(mat_khau_cu);
  if (!isValidPassword) {
    throw new AppError('Mật khẩu cũ không đúng', 400);
  }

  // Cập nhật mật khẩu mới
  await user.update({ 
    mat_khau: mat_khau_moi,
    nguoi_cap_nhap: user.email
  });

  res.json({
    success: true,
    message: 'Đổi mật khẩu thành công'
  });
}));

module.exports = router;
