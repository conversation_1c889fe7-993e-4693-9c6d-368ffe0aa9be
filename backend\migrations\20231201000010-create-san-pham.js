'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('san_pham', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      loai_san_pham_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'loai_san_pham',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      ten: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [2, 200]
        }
      },
      ma: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true
      },
      mo_ta: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      nhan_hieu_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'nhan_hieu',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      tag_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'tag',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      trang_thai: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: '1: active, 0: inactive'
      },
      nguoi_tao: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes (check if they exist first)
    try {
      await queryInterface.addIndex('san_pham', ['loai_san_pham_id']);
    } catch (error) {
      console.log('Index san_pham_loai_san_pham_id already exists');
    }

    try {
      await queryInterface.addIndex('san_pham', ['nhan_hieu_id']);
    } catch (error) {
      console.log('Index san_pham_nhan_hieu_id already exists');
    }

    try {
      await queryInterface.addIndex('san_pham', ['tag_id']);
    } catch (error) {
      console.log('Index san_pham_tag_id already exists');
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('san_pham');
  }
};
