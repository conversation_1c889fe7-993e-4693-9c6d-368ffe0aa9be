const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PhanCongNhanVienPhuTrach, sequelize } = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');
const bcrypt = require('bcryptjs');

/**
 * L<PERSON>y danh sách nhân viên
 */
exports.getEmployees = async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '',
    trang_thai,
    vai_tro_id
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { loai_nguoi_dung: 'nhan_vien' };

  // Tìm kiếm theo tên, email, số điện thoại
  if (search) {
    whereClause[Op.or] = [
      { ho_ten: { [Op.like]: `%${search}%` } },
      { email: { [Op.like]: `%${search}%` } },
      { so_dien_thoai: { [Op.like]: `%${search}%` } }
    ];
  }

  // Lọc theo trạng thái
  if (trang_thai) {
    whereClause.trang_thai = trang_thai;
  }

  // Tạo include cho các quan hệ
  const include = [
    {
      model: VaiTro,
      as: 'vaiTroList',
      attributes: ['id', 'ten_vai_tro', 'ma_vai_tro'],
      through: { attributes: [] }
    }
  ];

  // Nếu lọc theo vai trò
  if (vai_tro_id) {
    const employeesWithRole = await NguoiDungVaiTro.findAll({
      where: { vai_tro_id },
      attributes: ['nguoi_dung_id']
    });
    
    const employeeIds = employeesWithRole.map(e => e.nguoi_dung_id);
    
    if (employeeIds.length === 0) {
      return res.json({
        success: true,
        data: {
          employees: [],
          pagination: {
            total: 0,
            page: parseInt(page),
            limit: parseInt(limit)
          }
        }
      });
    }
    
    whereClause.id = { [Op.in]: employeeIds };
  }

  // Truy vấn danh sách nhân viên
  const { count, rows } = await NguoiDung.findAndCountAll({
    where: whereClause,
    include,
    offset,
    limit: parseInt(limit),
    distinct: true,
    order: [['ngay_tao', 'DESC']]
  });

  res.json({
    success: true,
    data: {
      employees: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    }
  });
};

/**
 * Lấy chi tiết nhân viên
 */
exports.getEmployee = async (req, res) => {
  const { id } = req.params;

  const employee = await NguoiDung.findOne({
    where: { 
      id,
      loai_nguoi_dung: 'nhan_vien'
    },
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList',
        attributes: ['id', 'ten_vai_tro', 'ma_vai_tro'],
        through: { attributes: [] }
      }
    ]
  });

  if (!employee) {
    throw new AppError('Nhân viên không tồn tại', 404);
  }

  // Lấy danh sách khách hàng được phân công
  const assignedCustomers = await NguoiDung.findAll({
    include: [
      {
        model: PhanCongNhanVienPhuTrach,
        as: 'phanCongNhanVien',
        where: { nhan_vien_id: id },
        required: true
      }
    ],
    where: { loai_nguoi_dung: 'khach_hang' }
  });

  // Loại bỏ thông tin mật khẩu
  const { mat_khau, ...employeeData } = employee.toJSON();

  res.json({
    success: true,
    data: {
      ...employeeData,
      khachHangPhuTrach: assignedCustomers
    }
  });
};

/**
 * Tạo nhân viên mới
 */
exports.createEmployee = async (req, res) => {
  const { 
    ho_ten, email, so_dien_thoai, mat_khau, 
    dia_chi, vai_tro_ids, trang_thai 
  } = req.body;

  const transaction = await sequelize.transaction();

  try {
    // Kiểm tra email đã tồn tại chưa
    if (email) {
      const existingEmail = await NguoiDung.findOne({
        where: { email },
        transaction
      });

      if (existingEmail) {
        await transaction.rollback();
        throw new AppError('Email đã tồn tại trong hệ thống', 400);
      }
    }

    // Kiểm tra số điện thoại đã tồn tại chưa
    if (so_dien_thoai) {
      const existingPhone = await NguoiDung.findOne({
        where: { so_dien_thoai },
        transaction
      });

      if (existingPhone) {
        await transaction.rollback();
        throw new AppError('Số điện thoại đã tồn tại trong hệ thống', 400);
      }
    }

    // Mã hóa mật khẩu
    const hashedPassword = mat_khau ? await bcrypt.hash(mat_khau, 10) : null;

    // Tạo nhân viên mới
    const newEmployee = await NguoiDung.create({
      ho_ten,
      email,
      so_dien_thoai,
      mat_khau: hashedPassword,
      dia_chi,
      trang_thai: trang_thai || 'active',
      loai_nguoi_dung: 'nhan_vien',
      nguoi_tao: req.user.username
    }, { transaction });

    // Nếu có vai trò, thêm vai trò cho nhân viên
    if (vai_tro_ids && vai_tro_ids.length > 0) {
      const roleAssignments = vai_tro_ids.map(vai_tro_id => ({
        nguoi_dung_id: newEmployee.id,
        vai_tro_id
      }));

      await NguoiDungVaiTro.bulkCreate(roleAssignments, { transaction });
    }

    await transaction.commit();

    // Loại bỏ thông tin mật khẩu
    const { mat_khau: _, ...employeeData } = newEmployee.toJSON();

    res.status(201).json({
      success: true,
      message: 'Tạo nhân viên thành công',
      data: employeeData
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Cập nhật nhân viên
 */
exports.updateEmployee = async (req, res) => {
  const { id } = req.params;
  const { 
    ho_ten, email, so_dien_thoai, mat_khau, 
    dia_chi, vai_tro_ids, trang_thai 
  } = req.body;

  const transaction = await sequelize.transaction();

  try {
    // Kiểm tra nhân viên tồn tại
    const employee = await NguoiDung.findOne({
      where: { 
        id,
        loai_nguoi_dung: 'nhan_vien'
      },
      transaction
    });

    if (!employee) {
      await transaction.rollback();
      throw new AppError('Nhân viên không tồn tại', 404);
    }

    // Kiểm tra email đã tồn tại chưa
    if (email && email !== employee.email) {
      const existingEmail = await NguoiDung.findOne({
        where: { 
          email,
          id: { [Op.ne]: id }
        },
        transaction
      });

      if (existingEmail) {
        await transaction.rollback();
        throw new AppError('Email đã tồn tại trong hệ thống', 400);
      }
    }

    // Kiểm tra số điện thoại đã tồn tại chưa
    if (so_dien_thoai && so_dien_thoai !== employee.so_dien_thoai) {
      const existingPhone = await NguoiDung.findOne({
        where: { 
          so_dien_thoai,
          id: { [Op.ne]: id }
        },
        transaction
      });

      if (existingPhone) {
        await transaction.rollback();
        throw new AppError('Số điện thoại đã tồn tại trong hệ thống', 400);
      }
    }

    // Mã hóa mật khẩu nếu có
    let hashedPassword = undefined;
    if (mat_khau) {
      hashedPassword = await bcrypt.hash(mat_khau, 10);
    }

    // Cập nhật thông tin nhân viên
    await employee.update({
      ho_ten: ho_ten || employee.ho_ten,
      email: email || employee.email,
      so_dien_thoai: so_dien_thoai || employee.so_dien_thoai,
      mat_khau: hashedPassword || employee.mat_khau,
      dia_chi: dia_chi !== undefined ? dia_chi : employee.dia_chi,
      trang_thai: trang_thai || employee.trang_thai,
      nguoi_cap_nhap: req.user.username,
      ngay_cap_nhap: new Date()
    }, { transaction });

    // Nếu có vai trò, cập nhật vai trò cho nhân viên
    if (vai_tro_ids !== undefined) {
      // Xóa tất cả vai trò hiện tại
      await NguoiDungVaiTro.destroy({
        where: { nguoi_dung_id: id },
        transaction
      });

      // Thêm vai trò mới
      if (vai_tro_ids && vai_tro_ids.length > 0) {
        const roleAssignments = vai_tro_ids.map(vai_tro_id => ({
          nguoi_dung_id: id,
          vai_tro_id
        }));

        await NguoiDungVaiTro.bulkCreate(roleAssignments, { transaction });
      }
    }

    await transaction.commit();

    res.json({
      success: true,
      message: 'Cập nhật nhân viên thành công'
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Xóa nhân viên
 */
exports.deleteEmployee = async (req, res) => {
  const { id } = req.params;

  const transaction = await sequelize.transaction();

  try {
    // Kiểm tra nhân viên tồn tại
    const employee = await NguoiDung.findOne({
      where: { 
        id,
        loai_nguoi_dung: 'nhan_vien'
      },
      transaction
    });

    if (!employee) {
      await transaction.rollback();
      throw new AppError('Nhân viên không tồn tại', 404);
    }

    // Kiểm tra nhân viên có đang phụ trách khách hàng nào không
    const assignments = await PhanCongNhanVienPhuTrach.findOne({
      where: { nhan_vien_id: id },
      transaction
    });

    if (assignments) {
      await transaction.rollback();
      throw new AppError('Nhân viên đang phụ trách khách hàng, không thể xóa', 400);
    }

    // Xóa vai trò của nhân viên
    await NguoiDungVaiTro.destroy({
      where: { nguoi_dung_id: id },
      transaction
    });

    // Xóa nhân viên
    await employee.destroy({ transaction });

    await transaction.commit();

    res.json({
      success: true,
      message: 'Xóa nhân viên thành công'
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Phân công nhân viên phụ trách khách hàng
 */
exports.assignCustomers = async (req, res) => {
  const { id } = req.params; // ID của nhân viên
  const { khach_hang_ids } = req.body;

  const transaction = await sequelize.transaction();

  try {
    // Kiểm tra nhân viên tồn tại
    const employee = await NguoiDung.findOne({
      where: { 
        id,
        loai_nguoi_dung: 'nhan_vien'
      },
      transaction
    });

    if (!employee) {
      await transaction.rollback();
      throw new AppError('Nhân viên không tồn tại', 404);
    }

    // Xóa tất cả phân công hiện tại
    await PhanCongNhanVienPhuTrach.destroy({
      where: { nhan_vien_id: id },
      transaction
    });

    // Thêm phân công mới
    if (khach_hang_ids && khach_hang_ids.length > 0) {
      const assignments = khach_hang_ids.map(khach_hang_id => ({
        nguoi_dung_id: khach_hang_id,
        nhan_vien_id: id,
        ngay_phan_cong: new Date()
      }));

      await PhanCongNhanVienPhuTrach.bulkCreate(assignments, { transaction });
    }

    await transaction.commit();

    res.json({
      success: true,
      message: 'Phân công khách hàng thành công'
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Lấy danh sách khách hàng được phân công cho nhân viên
 */
exports.getAssignedCustomers = async (req, res) => {
  const { id } = req.params; // ID của nhân viên
  const { page = 1, limit = 10 } = req.query;

  const offset = (page - 1) * limit;

  // Kiểm tra nhân viên tồn tại
  const employee = await NguoiDung.findOne({
    where: { 
      id,
      loai_nguoi_dung: 'nhan_vien'
    }
  });

  if (!employee) {
    throw new AppError('Nhân viên không tồn tại', 404);
  }

  // Lấy danh sách khách hàng được phân công
  const { count, rows } = await NguoiDung.findAndCountAll({
    include: [
      {
        model: PhanCongNhanVienPhuTrach,
        as: 'phanCongNhanVien',
        where: { nhan_vien_id: id },
        required: true
      }
    ],
    where: { loai_nguoi_dung: 'khach_hang' },
    offset,
    limit: parseInt(limit),
    distinct: true,
    order: [['ho_ten', 'ASC']]
  });

  res.json({
    success: true,
    data: {
      customers: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    }
  });
};