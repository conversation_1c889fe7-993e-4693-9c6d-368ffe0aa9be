'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('lich_su_kho', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      phien_ban_san_pham_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'phien_ban_san_pham',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      kho_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'kho_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      loai_giao_dich: {
        type: Sequelize.ENUM('nhap', 'xuat', 'chuyen', 'dieu_chinh', 'kiem_ke'),
        allowNull: false,
        defaultValue: 'dieu_chinh',
        comment: 'Loại giao dịch: nhap, xuat, chuyen, dieu_chinh, kiem_ke'
      },
      so_luong_truoc: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Số lượng trước khi thay đổi'
      },
      so_luong_thay_doi: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Số lượng thay đổi (có thể âm)'
      },
      so_luong_sau: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Số lượng sau khi thay đổi'
      },
      gia_von: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        comment: 'Giá vốn tại thời điểm giao dịch'
      },
      ly_do: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Lý do thay đổi'
      },
      don_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'ID đơn hàng (nếu có)'
      },
      phieu_kiem_ke_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'ID phiếu kiểm kê (nếu có)'
      },
      kho_chuyen_den_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'kho_hang',
          key: 'id'
        },
        comment: 'ID kho chuyển đến (nếu là chuyển kho)'
      },
      nguoi_thuc_hien: {
        type: Sequelize.STRING(100),
        allowNull: false,
        defaultValue: 'system',
        comment: 'Người thực hiện giao dịch'
      },
      ghi_chu: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Ghi chú thêm'
      },
      ngay_thuc_hien: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: 'Ngày thực hiện giao dịch'
      },
      nguoi_tao: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('lich_su_kho', ['phien_ban_san_pham_id']);
    await queryInterface.addIndex('lich_su_kho', ['kho_hang_id']);
    await queryInterface.addIndex('lich_su_kho', ['loai_giao_dich']);
    await queryInterface.addIndex('lich_su_kho', ['ngay_thuc_hien']);
    await queryInterface.addIndex('lich_su_kho', ['don_hang_id']);
    await queryInterface.addIndex('lich_su_kho', ['kho_chuyen_den_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('lich_su_kho');
  }
};
