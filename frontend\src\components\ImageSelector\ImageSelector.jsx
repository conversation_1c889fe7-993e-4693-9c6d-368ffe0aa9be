import React, { useState } from 'react';
import { Modal, Image, Row, Col, Button, Empty } from 'antd';
import { CheckOutlined } from '@ant-design/icons';

const ImageSelector = ({ 
  visible, 
  onCancel, 
  onSelect, 
  images = [], 
  selectedImage = null,
  title = "Chọn ảnh đại diện" 
}) => {
  const [selectedImageId, setSelectedImageId] = useState(selectedImage?.uid || null);

  const handleSelect = () => {
    const selected = images.find(img => img.uid === selectedImageId);
    if (selected) {
      onSelect(selected);
    }
    onCancel();
  };

  const handleImageClick = (imageId) => {
    setSelectedImageId(imageId);
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Hủy
        </Button>,
        <Button 
          key="select" 
          type="primary" 
          onClick={handleSelect}
          disabled={!selectedImageId}
        >
          <PERSON><PERSON><PERSON>nh này
        </Button>,
      ]}
    >
      {images.length === 0 ? (
        <Empty 
          description="Chưa có ảnh nào. Vui lòng thêm ảnh sản phẩm trước." 
          style={{ margin: '40px 0' }}
        />
      ) : (
        <Row gutter={[16, 16]}>
          {images.map((image) => (
            <Col xs={12} sm={8} md={6} key={image.uid}>
              <div
                style={{
                  position: 'relative',
                  cursor: 'pointer',
                  border: selectedImageId === image.uid ? '3px solid #1890ff' : '1px solid #d9d9d9',
                  borderRadius: 8,
                  overflow: 'hidden',
                  transition: 'all 0.3s',
                }}
                onClick={() => handleImageClick(image.uid)}
              >
                <Image
                  src={image.thumbnail_url || image.url}
                  alt={image.name}
                  preview={false}
                  style={{
                    width: '100%',
                    height: 120,
                    objectFit: 'cover',
                  }}
                />
                
                {selectedImageId === image.uid && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(24, 144, 255, 0.3)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <CheckOutlined 
                      style={{ 
                        fontSize: 24, 
                        color: '#fff',
                        backgroundColor: '#1890ff',
                        borderRadius: '50%',
                        padding: 8,
                      }} 
                    />
                  </div>
                )}
              </div>
            </Col>
          ))}
        </Row>
      )}
    </Modal>
  );
};

export default ImageSelector;
