# 📊 Báo Cáo Công Nợ - Dữ Liệu Thật

## 🎯 Mục Tiêu
Cập nhật phần báo cáo công nợ để sử dụng dữ liệu thật từ database thay vì dữ liệu fake.

## ✅ Những Gì Đã Hoàn Thành

### 1. **Backend API Updates**

#### **Updated Controller: `debtController.js`**
- ✅ **`getDebtReport()`**: Hoàn toàn mới với dữ liệu thật
  - Tính tổng công nợ hiện tại từ bảng `CongNoNguoiDung`
  - Tính tổng thanh toán từ bảng `Payment`
  - <PERSON><PERSON>h công nợ quá hạn (đơn hàng > 30 ngày)
  - Tính tỷ lệ thu hồi và trung bình công nợ/khách hàng
  - Tạo dữ liệu biểu đồ theo 6 tháng gần nhất
  - Tạo dữ liệu pie chart theo tỷ lệ thanh toán
  - Lấy top 5 khách hàng nợ nhiều nhất

#### **New Route: `/api/debt/seed-test-data`**
- ✅ Endpoint để tạo dữ liệu test cho báo cáo
- ✅ Tạo 5 khách hàng test
- ✅ Tạo đơn hàng với các trạng thái khác nhau
- ✅ Tạo công nợ và thanh toán test
- ✅ Tạo dữ liệu lịch sử 6 tháng

### 2. **Frontend Updates**

#### **Updated Page: `DebtReport.jsx`**
- ✅ Sử dụng dữ liệu thật từ API thay vì fallback data
- ✅ Thêm button "Tạo dữ liệu test" 
- ✅ Hiển thị empty state khi không có dữ liệu
- ✅ Auto refresh sau khi tạo dữ liệu test

### 3. **Database Schema**
- ✅ Sử dụng các bảng hiện có:
  - `CongNoNguoiDung` - Công nợ khách hàng
  - `Payment` - Thanh toán
  - `DonHang` - Đơn hàng
  - `NguoiDung` - Khách hàng

## 🚀 Cách Sử Dụng

### **1. Start Backend Server**
```bash
cd SaleSysBE
npm start
```

### **2. Start Frontend Server**
```bash
cd SaleSysFE
npm run dev
```

### **3. Tạo Dữ Liệu Test**
Có 3 cách để tạo dữ liệu test:

#### **Cách 1: Từ Frontend (Khuyến nghị)**
1. Mở trang Báo cáo công nợ: `/accounting/debt-report`
2. Click button "Tạo dữ liệu test"
3. Chờ thông báo thành công
4. Dữ liệu sẽ tự động refresh

#### **Cách 2: API Call Trực Tiếp**
```bash
curl -X POST http://localhost:5000/api/debt/seed-test-data \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### **Cách 3: Chạy Script**
```bash
cd SaleSysBE
node scripts/seedDebtReportData.js
```

### **4. Test API**
```bash
cd SaleSysBE
node test-debt-report.js
```

## 📊 Dữ Liệu Được Tạo

### **Khách Hàng Test (5 người)**
- Nguyễn Văn A - Công nợ: 15,000,000 VND
- Trần Thị B - Công nợ: 12,000,000 VND  
- Lê Văn C - Công nợ: 10,000,000 VND
- Phạm Thị D - Công nợ: 8,000,000 VND
- Hoàng Văn E - Công nợ: 7,000,000 VND

### **Đơn Hàng Test**
- Các trạng thái: `da_xac_nhan`, `da_dong_goi`, `da_giao`, `hoan_thanh`
- Thời gian: Từ 50 ngày trước đến hiện tại
- Tổng giá trị: ~67,000,000 VND

### **Thanh Toán Test**
- Loại: `thu` (thu tiền)
- Phương thức: `cash`, `transfer`
- Tổng thanh toán: ~15,000,000 VND

### **Dữ Liệu Lịch Sử**
- 6 tháng dữ liệu biểu đồ
- Đơn hàng và thanh toán theo từng tháng

## 🔍 API Endpoints

### **GET /api/debt/report**
Lấy báo cáo công nợ với dữ liệu thật

**Query Parameters:**
- `start_date` (optional): Ngày bắt đầu (YYYY-MM-DD)
- `end_date` (optional): Ngày kết thúc (YYYY-MM-DD)
- `type` (optional): Loại báo cáo (overview, detailed, aging)

**Response:**
```json
{
  "success": true,
  "data": {
    "stats": {
      "totalDebt": 52000000,
      "overdueDebt": 15000000,
      "collectionRate": 22,
      "avgDebtPerCustomer": 10400000
    },
    "chartData": [...],
    "pieData": [...],
    "topDebtors": [...]
  }
}
```

### **POST /api/debt/seed-test-data**
Tạo dữ liệu test cho báo cáo

**Response:**
```json
{
  "success": true,
  "message": "Tạo dữ liệu test thành công"
}
```

## 🎨 Frontend Features

### **Real-time Data**
- ✅ Dữ liệu từ database thật
- ✅ Auto refresh sau khi tạo test data
- ✅ Loading states

### **Empty States**
- ✅ Hiển thị khi không có dữ liệu biểu đồ
- ✅ Hiển thị khi không có khách hàng
- ✅ Button để tạo dữ liệu test

### **Data Visualization**
- ✅ Area chart: Xu hướng công nợ theo tháng
- ✅ Pie chart: Tỷ lệ thanh toán
- ✅ Statistics cards: Các chỉ số tổng quan
- ✅ Table: Top khách hàng nợ nhiều nhất

## 🔧 Troubleshooting

### **Lỗi "Không có dữ liệu"**
- Chạy seed data script hoặc click "Tạo dữ liệu test"

### **Lỗi API 404**
- Kiểm tra server backend đã chạy chưa
- Kiểm tra routes đã được import đúng chưa

### **Lỗi Authentication**
- Đảm bảo đã đăng nhập và có token hợp lệ

## 🎉 Kết Quả

✅ **Báo cáo công nợ hoàn toàn sử dụng dữ liệu thật**
✅ **Dễ dàng tạo dữ liệu test để demo**
✅ **Performance tốt với database queries được tối ưu**
✅ **UI/UX thân thiện với empty states**
✅ **Có thể mở rộng thêm các loại báo cáo khác**
