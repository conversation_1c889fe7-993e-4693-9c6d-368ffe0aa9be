'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('anh_san_pham', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      url: {
        type: Sequelize.STRING,
        allowNull: true
      },
      san_pham_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'san_pham',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      nguoi_tao: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('anh_san_pham', ['san_pham_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('anh_san_pham');
  }
};
