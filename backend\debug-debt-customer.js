require('dotenv').config();
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, CongNoNguoiD<PERSON>, DonHang, Payment, sequelize } = require('./models');

async function debugCustomerDebt() {
  try {
    console.log('🔍 Debugging customer debt for customer ID 32...\n');

    // 1. Lấy thông tin khách hàng
    const customer = await NguoiDung.findByPk(32, {
      include: [
        {
          model: CongNoNguoiDung,
          as: 'congNo'
        },
        {
          model: DonHang,
          as: 'donH<PERSON><PERSON><PERSON><PERSON><PERSON>ang',
          attributes: ['id', 'ma_don_hang', 'tong_phai_tra', 'tong_da_tra', 'con_phai_tra', 'trang_thai', 'ngay_ban']
        }
      ]
    });

    if (!customer) {
      console.log('❌ Customer not found!');
      return;
    }

    console.log('👤 Customer Info:');
    console.log(`   ID: ${customer.id}`);
    console.log(`   Name: ${customer.ho_ten}`);
    console.log(`   Phone: ${customer.so_dien_thoai}`);
    console.log(`   Email: ${customer.email}`);

    // 2. Kiểm tra bảng công nợ
    console.log('\n💰 Debt Record (cong_no_nguoi_dung):');
    if (customer.congNo) {
      console.log(`   ✅ Found debt record:`);
      console.log(`   - ID: ${customer.congNo.id}`);
      console.log(`   - Total Debt: ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(customer.congNo.tong_cong_no || 0)}`);
      console.log(`   - Raw Value: ${customer.congNo.tong_cong_no}`);
      console.log(`   - Created: ${customer.congNo.createdAt}`);
      console.log(`   - Updated: ${customer.congNo.updatedAt}`);
    } else {
      console.log('   ❌ No debt record found in cong_no_nguoi_dung table');
    }

    // 3. Kiểm tra đơn hàng
    console.log('\n📋 Orders (don_hang):');
    const orders = customer.donHangKhachHang || [];
    if (orders.length > 0) {
      console.log(`   ✅ Found ${orders.length} order(s):`);
      orders.forEach((order, index) => {
        const conPhaiTra = order.con_phai_tra || 0;
        console.log(`   ${index + 1}. Order ${order.ma_don_hang}:`);
        console.log(`      - Total: ${new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(order.tong_phai_tra || 0)}`);
        console.log(`      - Paid: ${new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(order.tong_da_tra || 0)}`);
        console.log(`      - Remaining (con_phai_tra): ${new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(conPhaiTra)} ${conPhaiTra < 0 ? '(NEGATIVE!)' : ''}`);
        console.log(`      - Status: ${order.trang_thai}`);
        console.log(`      - Date: ${order.ngay_ban}`);
      });

      // Tính tổng con_phai_tra từ đơn hàng
      const totalConPhaiTra = orders.reduce((sum, order) => sum + (order.con_phai_tra || 0), 0);
      console.log(`\n   📊 Total con_phai_tra from orders: ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(totalConPhaiTra)} ${totalConPhaiTra < 0 ? '(NEGATIVE!)' : ''}`);
    } else {
      console.log('   ❌ No orders found');
    }

    // 4. Kiểm tra payments
    console.log('\n💳 Payments:');
    const payments = await Payment.findAll({
      where: { customer_id: 32 },
      order: [['created_at', 'DESC']]
    });

    if (payments.length > 0) {
      console.log(`   ✅ Found ${payments.length} payment(s):`);
      payments.forEach((payment, index) => {
        const typeText = payment.type === 'thu' ? 'Thu tiền' : 'Chi tiền';
        console.log(`   ${index + 1}. ${typeText}: ${new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(payment.amount)}`);
        console.log(`      - Method: ${payment.payment_method}`);
        console.log(`      - Status: ${payment.status}`);
        console.log(`      - Date: ${payment.created_at}`);
        console.log(`      - Note: ${payment.note || 'N/A'}`);
      });
    } else {
      console.log('   ❌ No payments found');
    }

    // 5. Phân tích vấn đề
    console.log('\n🔍 ANALYSIS:');
    
    const debtFromTable = customer.congNo ? (customer.congNo.tong_cong_no || 0) : 0;
    const debtFromOrders = orders.reduce((sum, order) => sum + (order.con_phai_tra || 0), 0);
    
    console.log(`📊 Debt from cong_no_nguoi_dung table: ${new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(debtFromTable)}`);
    
    console.log(`📊 Debt from orders (con_phai_tra): ${new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(debtFromOrders)}`);

    if (Math.abs(debtFromTable - debtFromOrders) > 1) {
      console.log('⚠️  MISMATCH DETECTED!');
      console.log('   The debt in cong_no_nguoi_dung table does not match the sum of con_phai_tra from orders');
      console.log('   This could be why the frontend shows 0 instead of the negative amount');
      
      // Đề xuất sửa chữa
      console.log('\n🔧 SUGGESTED FIX:');
      if (!customer.congNo) {
        console.log('   1. Create debt record in cong_no_nguoi_dung table');
        console.log(`   2. Set tong_cong_no = ${debtFromOrders}`);
      } else {
        console.log('   1. Update debt record in cong_no_nguoi_dung table');
        console.log(`   2. Change tong_cong_no from ${debtFromTable} to ${debtFromOrders}`);
      }
    } else {
      console.log('✅ Debt amounts match between tables');
    }

    // 6. Test fix
    console.log('\n🔧 ATTEMPTING TO FIX...');
    
    if (!customer.congNo) {
      // Tạo record mới
      const newDebtRecord = await CongNoNguoiDung.create({
        nguoi_dung_id: 32,
        tong_cong_no: debtFromOrders
      });
      console.log(`✅ Created new debt record with amount: ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(debtFromOrders)}`);
    } else if (Math.abs(debtFromTable - debtFromOrders) > 1) {
      // Cập nhật record hiện tại
      await customer.congNo.update({
        tong_cong_no: debtFromOrders
      });
      console.log(`✅ Updated debt record from ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(debtFromTable)} to ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(debtFromOrders)}`);
    } else {
      console.log('ℹ️  No fix needed - amounts already match');
    }

    console.log('\n🎯 Try refreshing the frontend now!');

  } catch (error) {
    console.error('❌ Error debugging customer debt:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy debug
debugCustomerDebt();
