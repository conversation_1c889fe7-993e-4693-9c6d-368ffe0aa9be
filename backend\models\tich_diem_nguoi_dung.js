'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class TichDiemNguoiDung extends Model {
    static associate(models) {
      // Quan hệ với người dùng (1-1)
      TichDiemNguoiDung.belongsTo(models.NguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'nguoiDung'
      });
    }
  }

  TichDiemNguoiDung.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nguoi_dung_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    diem_hien_tai: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    hang_tich_diem: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ngay_het_han_the: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    gia_tri_con_lai_len_hang: {
      type: DataTypes.DOUBLE,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'TichDiemNguoiDung',
    tableName: 'tich_diem_nguoi_dung',
    timestamps: false
  });

  return TichDiemNguoiDung;
};
