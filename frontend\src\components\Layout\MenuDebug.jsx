import React from 'react';
import { usePermissions } from '../../contexts/PermissionContext';

const MenuDebug = () => {
  const { getAccessibleMenuItems } = usePermissions();
  const menuItems = getAccessibleMenuItems();

  console.log('Menu Items:', menuItems);

  return (
    <div style={{ padding: 20, background: '#f0f0f0', margin: 10 }}>
      <h3>Menu Debug Info</h3>
      <pre style={{ background: 'white', padding: 10, borderRadius: 4 }}>
        {JSON.stringify(menuItems, null, 2)}
      </pre>
    </div>
  );
};

export default MenuDebug;
