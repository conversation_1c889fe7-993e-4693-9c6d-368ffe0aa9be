import { useQuery, useMutation, useQueryClient } from 'react-query';
import { message } from 'antd';
import { customersAPI, testAPI } from '../services/api';

// Query keys
const CUSTOMER_QUERY_KEYS = {
  customers: () => ['customers'],
  customer: (id) => ['customers', id],
};

// Get customers list
export const useCustomers = (params = {}) => {
  return useQuery(
    [...CUSTOMER_QUERY_KEYS.customers(), params],
    () => testAPI.getCustomers(params),
    {
      select: (response) => {
        console.log('👥 Customers API response:', response);
        // API test trả về {success: true, data: {customers: [...], pagination: {...}}}
        return Array.isArray(response?.data?.customers) ? response.data.customers : [];
      },
      staleTime: 30 * 1000, // 30 seconds
    }
  );
};

// Get single customer
export const useCustomer = (id) => {
  return useQuery(
    CUSTOMER_QUERY_KEYS.customer(id),
    () => customersAPI.getCustomer(id),
    {
      enabled: !!id,
      select: (data) => data.data,
      staleTime: 30 * 1000,
    }
  );
};

// Create customer mutation
export const useCreateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation(customersAPI.createCustomer, {
    onSuccess: (data) => {
      message.success('Tạo khách hàng thành công!');
      queryClient.invalidateQueries(CUSTOMER_QUERY_KEYS.customers());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi tạo khách hàng');
    },
  });
};

// Update customer mutation
export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, ...data }) => customersAPI.updateCustomer(id, data),
    {
      onSuccess: (data, variables) => {
        message.success('Cập nhật khách hàng thành công!');
        queryClient.invalidateQueries(CUSTOMER_QUERY_KEYS.customer(variables.id));
        queryClient.invalidateQueries(CUSTOMER_QUERY_KEYS.customers());
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật khách hàng');
      },
    }
  );
};

// Delete customer mutation
export const useDeleteCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation(customersAPI.deleteCustomer, {
    onSuccess: () => {
      message.success('Xóa khách hàng thành công!');
      queryClient.invalidateQueries(CUSTOMER_QUERY_KEYS.customers());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa khách hàng');
    },
  });
};

export { CUSTOMER_QUERY_KEYS };
