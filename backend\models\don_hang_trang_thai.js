'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DonHangTrangThai extends Model {
    static associate(models) {
      // Quan hệ với đơn hàng
      DonHangTrangThai.belongsTo(models.DonHang, {
        foreignKey: 'don_hang_id',
        as: 'donHang'
      });

      // Quan hệ với nhân viên
      <PERSON>hai.belongsTo(models.NguoiDung, {
        foreignKey: 'nhan_vien_id',
        as: 'nhanVien'
      });
    }
  }

  DonHangTrangThai.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    don_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'don_hang',
        key: 'id'
      }
    },
    trang_thai: {
      type: DataTypes.ENUM('cho_xu_ly', 'da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'huy'),
      allowNull: false
    },
    thoi_gian: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    nhan_vien_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    ghi_chu: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'DonHangTrangThai',
    tableName: 'don_hang_trang_thai',
    timestamps: false
  });

  return DonHangTrangThai;
};
