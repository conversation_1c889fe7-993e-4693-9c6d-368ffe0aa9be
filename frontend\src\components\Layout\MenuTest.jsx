import React, { useState } from 'react';
import { Menu } from 'antd';
import {
  DashboardOutlined,
  ShoppingOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  HomeOutlined,
  BarChartOutlined,
  SettingOutlined
} from '@ant-design/icons';

const MenuTest = () => {
  const [openKeys, setOpenKeys] = useState(['products', 'system']);

  const testMenuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: 'Tổng quan'
    },
    {
      key: 'products',
      icon: <ShoppingOutlined />,
      label: 'Sản phẩm',
      children: [
        {
          key: 'products-list',
          label: '<PERSON>h sách sản phẩm'
        },
        {
          key: 'product-categories',
          label: 'Loại sản phẩm'
        },
        {
          key: 'product-brands',
          label: 'Nhãn hiệu'
        }
      ]
    },
    {
      key: 'orders',
      icon: <ShoppingCartOutlined />,
      label: 'Đơn hàng'
    },
    {
      key: 'system',
      icon: <SettingOutlined />,
      label: '<PERSON><PERSON> thống',
      children: [
        {
          key: 'users',
          label: 'Ngư<PERSON><PERSON> dùng'
        },
        {
          key: 'roles',
          label: '<PERSON>ai trò'
        },
        {
          key: 'permissions',
          label: 'Quyền'
        }
      ]
    }
  ];

  const handleOpenChange = (keys) => {
    console.log('Open keys changed:', keys);
    setOpenKeys(keys);
  };

  return (
    <div style={{ width: 256, background: '#001529' }}>
      <h3 style={{ color: 'white', padding: 16 }}>Menu Test</h3>
      <Menu
        theme="dark"
        mode="inline"
        openKeys={openKeys}
        onOpenChange={handleOpenChange}
        items={testMenuItems}
        style={{ borderRight: 0 }}
      />
      <div style={{ color: 'white', padding: 16 }}>
        <p>Open Keys: {JSON.stringify(openKeys)}</p>
      </div>
    </div>
  );
};

export default MenuTest;
