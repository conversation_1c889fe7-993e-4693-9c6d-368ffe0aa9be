'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('nhom_khach_hang', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      ten_nhom: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [2, 100]
        }
      },
      mo_ta: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      ma_nhom: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('nhom_khach_hang', ['ma_nhom']);
    await queryInterface.addIndex('nhom_khach_hang', ['ten_nhom']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('nhom_khach_hang');
  }
};
