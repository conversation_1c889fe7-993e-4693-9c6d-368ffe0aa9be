// Order status constants for UI components
export const ORDER_STATUS = [
  {
    value: 'cho_xu_ly',
    label: 'Chờ xử lý',
    color: 'orange'
  },
  {
    value: 'da_xac_nhan',
    label: '<PERSON>ã xác nhận',
    color: 'blue'
  },
  {
    value: 'da_dong_goi',
    label: 'Đã đóng gói',
    color: 'cyan'
  },
  {
    value: 'da_giao',
    label: 'Đã giao cho ĐVVC',
    color: 'purple'
  },
  {
    value: 'hoan_thanh',
    label: 'Hoàn thành',
    color: 'green'
  },
  {
    value: 'huy',
    label: 'Hủy',
    color: 'red'
  },
  {
    value: 'hoan_hang',
    label: 'Hoàn hàng',
    color: 'magenta'
  }
];

// Delivery methods
export const DELIVERY_METHODS = [
  {
    value: 'store_pickup',
    label: 'Đây qua hàng vận chuyển'
  },
  {
    value: 'delivery',
    label: '<PERSON><PERSON><PERSON> vận chuyển ngoài'
  },
  {
    value: 'customer_pickup',
    label: '<PERSON><PERSON><PERSON><PERSON> nhận tại cửa hàng'
  },
  {
    value: 'later_delivery',
    label: 'Giao hàng sau'
  }
];

// Order sources
export const ORDER_SOURCES = [
  {
    value: 'truc_tiep',
    label: 'Trực tiếp'
  },
  {
    value: 'online',
    label: 'Online'
  },
  {
    value: 'dien_thoai',
    label: 'Điện thoại'
  },
  {
    value: 'app',
    label: 'Ứng dụng'
  }
];

// Discount types
export const DISCOUNT_TYPES = [
  {
    value: 'amount',
    label: 'Giảm theo số tiền'
  },
  {
    value: 'percent',
    label: 'Giảm theo phần trăm'
  }
];

// Payment status
export const PAYMENT_STATUS = [
  {
    value: 'chua_thanh_toan',
    label: 'Chưa thanh toán',
    color: 'red'
  },
  {
    value: 'thanh_toan_mot_phan',
    label: 'Thanh toán một phần',
    color: 'orange'
  },
  {
    value: 'da_thanh_toan',
    label: 'Đã thanh toán',
    color: 'green'
  }
];

// Helper functions
export const getOrderStatusInfo = (status) => {
  return ORDER_STATUS.find(s => s.value === status) || ORDER_STATUS[0];
};

export const getDeliveryMethodInfo = (method) => {
  return DELIVERY_METHODS.find(m => m.value === method) || DELIVERY_METHODS[0];
};

export const getOrderSourceInfo = (source) => {
  return ORDER_SOURCES.find(s => s.value === source) || ORDER_SOURCES[0];
};

export const getPaymentStatusInfo = (status) => {
  return PAYMENT_STATUS.find(s => s.value === status) || PAYMENT_STATUS[0];
};
