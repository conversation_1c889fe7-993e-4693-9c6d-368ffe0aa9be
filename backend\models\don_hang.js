'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DonHang extends Model {
    static associate(models) {
      // Quan hệ với khách hàng
      DonHang.belongsTo(models.NguoiDung, {
        foreignKey: 'khach_hang_id',
        as: 'khachHang'
      });

      // Quan hệ với kho hàng
      DonHang.belongsTo(models.KhoHang, {
        foreignKey: 'kho_hang_id',
        as: 'khoHang'
      });

      // Quan hệ với nhân viên bán
      <PERSON>.belongsTo(models.NguoiDung, {
        foreignKey: 'nhan_vien_ban_id',
        as: 'nhanVienBan'
      });

      // Quan hệ với sản phẩm đơn hàng (1-n)
      DonHang.hasMany(models.DonHangSanPham, {
        foreignKey: 'don_hang_id',
        as: 'sanPhamList'
      });

      // <PERSON>uan hệ với thanh toán (1-n)
      DonHang.hasMany(models.DonHangThanh<PERSON><PERSON>, {
        foreignKey: 'don_hang_id',
        as: 'thanhToanList'
      });

      // Quan hệ với giao hàng (1-1)
      DonHang.hasOne(models.DonHangGiaoHang, {
        foreignKey: 'don_hang_id',
        as: 'giaoHang'
      });

      // Quan hệ với địa chỉ giao (1-1)
      DonHang.hasOne(models.DonHangDiaChiGiao, {
        foreignKey: 'don_hang_id',
        as: 'diaChiGiao'
      });

      // Quan hệ với trạng thái (1-n)
      DonHang.hasMany(models.DonHangTrangThai, {
        foreignKey: 'don_hang_id',
        as: 'trangThaiList'
      });

      // Quan hệ với tag (n-n)
      DonHang.belongsToMany(models.Tag, {
        through: models.DonHangTag,
        foreignKey: 'don_hang_id',
        otherKey: 'tag_id',
        as: 'tagList'
      });
    }
  }

  DonHang.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ma_don_hang: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: 'SONxxxxx'
    },
    khach_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    kho_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'kho_hang',
        key: 'id'
      }
    },
    nhan_vien_ban_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    nguon_don_hang: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'web, pos, facebook...'
    },
    chinh_sach_gia: {
      type: DataTypes.STRING,
      allowNull: true
    },
    trang_thai: {
      type: DataTypes.ENUM('cho_xu_ly', 'da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'huy', 'hoan_hang'),
      defaultValue: 'cho_xu_ly'
    },
    tong_tien: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    chiet_khau: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    phi_giao_hang: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    ma_giam_gia: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tong_phai_tra: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    tong_da_tra: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    con_phai_tra: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    tien_coc: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0,
      comment: 'Tiền cọc khách hàng đã trả'
    },
    tien_cod: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0,
      comment: 'Tiền COD (thu hộ)'
    },
    ghi_chu: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    tags: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ngay_ban: {
      type: DataTypes.DATE,
      allowNull: true
    },
    han_giao_hang: {
      type: DataTypes.DATE,
      allowNull: true
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'DonHang',
    tableName: 'don_hang',
    timestamps: false, // Disable timestamps for now to avoid column issues
    hooks: {
      beforeCreate: async (donHang) => {
        if (!donHang.ma_don_hang) {
          // Tự động sinh mã đơn hàng
          const count = await DonHang.count();
          donHang.ma_don_hang = `SON${String(count + 1).padStart(6, '0')}`;
        }
      }
    }
  });

  return DonHang;
};
