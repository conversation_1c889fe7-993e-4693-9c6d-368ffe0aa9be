import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  InputNumber,
  Form,
  Row,
  Col,
  Typography,
  Modal,
  message,
  Popconfirm,
  Select,
  DatePicker,
  Image,
  Divider
} from 'antd';
import {
  ArrowLeftOutlined,
  CheckOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  DeleteOutlined,
  SearchOutlined
} from '@ant-design/icons';
import {
  useStockCheck,
  useStockCheckDetails,
  useUpdateStockCheck,
  useUpdateStockCheckItem,
  useCompleteStockCheck,
  useRemoveProductFromStockCheck,
  useWarehouses,
  useAddProductToStockCheck
} from '../../hooks/useWarehouses';
import { useQuery } from 'react-query';
import { warehousesAPI } from '../../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const StockCheckEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [editingItem, setEditingItem] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [showAddProductsModal, setShowAddProductsModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [modalSearchText, setModalSearchText] = useState('');

  // API hooks
  const { data: warehouses = [] } = useWarehouses();
  const {
    data: stockCheckData,
    isLoading: detailsLoading,
    refetch
  } = useStockCheckDetails(id, {
    page: currentPage,
    limit: pageSize,
    search: searchText
  });

  const updateStockCheckMutation = useUpdateStockCheck();
  const updateItemMutation = useUpdateStockCheckItem();
  const completeStockCheckMutation = useCompleteStockCheck();
  const removeProductMutation = useRemoveProductFromStockCheck();
  const addProductMutation = useAddProductToStockCheck();

  const stockCheck = stockCheckData?.stockCheck || {};
  const details = stockCheckData?.details || [];
  const pagination = stockCheckData?.pagination || {};

  // Debug logs
  

  // Get available products for selected warehouse
  const selectedWarehouseId = stockCheck.kho_hang_id;
  const { data: availableProducts = [], isLoading: productsLoading } = useQuery(
    ['available-products', id, selectedWarehouseId],
    () => {
      if (!id || !selectedWarehouseId) return Promise.resolve({ data: [] });
      return warehousesAPI.getAvailableProducts(id);
    },
    {
      enabled: !!id && !!selectedWarehouseId,
      select: (data) => {
        console.log('🛒 Available products raw data:', data);
        const result = data?.data || [];
        console.log('🛒 Available products processed:', result);
        console.log('🛒 Available products isArray:', Array.isArray(result));
        // Đảm bảo luôn trả về array
        return Array.isArray(result) ? result : [];
      }
    }
  );

  // Initialize form with stock check data
  useEffect(() => {
    if (stockCheck && Object.keys(stockCheck).length > 0) {
      const formData = {
        ten_kiem_ke: stockCheck.ten_kiem_ke,
        kho_hang_id: stockCheck.kho_hang_id,
        ngay_bat_dau: stockCheck.ngay_bat_dau ? dayjs(stockCheck.ngay_bat_dau) : null,
        ngay_ket_thuc: stockCheck.ngay_ket_thuc ? dayjs(stockCheck.ngay_ket_thuc) : null,
        nguoi_kiem_ke: stockCheck.nguoi_kiem_ke,
        ghi_chu: stockCheck.ghi_chu
      };
      form.setFieldsValue(formData);
    }
  }, [stockCheck, form]);

  // Handle save stock check info
  const handleSaveStockCheck = async () => {
    try {
      const values = await form.validateFields();
      const updateData = {
        ten_kiem_ke: values.ten_kiem_ke,
        kho_hang_id: values.kho_hang_id,
        ngay_bat_dau: values.ngay_bat_dau?.format('YYYY-MM-DD'),
        ngay_ket_thuc: values.ngay_ket_thuc?.format('YYYY-MM-DD'),
        nguoi_kiem_ke: values.nguoi_kiem_ke,
        ghi_chu: values.ghi_chu
      };

      await updateStockCheckMutation.mutateAsync({ id, data: updateData });
      message.success('Cập nhật phiếu kiểm kê thành công!');
      setIsEditing(false);
    } catch (error) {
      message.error('Có lỗi xảy ra khi cập nhật phiếu kiểm kê');
    }
  };

  // Handle complete stock check
  const handleCompleteStockCheck = async () => {
    try {
      await completeStockCheckMutation.mutateAsync(id);
      refetch();
    } catch (error) {
      // Error handled by mutation
    }
  };

  // Get status tag
  const getStockCheckStatusTag = (status) => {
    const statusConfig = {
      'ke_hoach': { color: 'blue', text: 'Kế hoạch' },
      'dang_kiem': { color: 'orange', text: 'Đang kiểm' },
      'dang_thuc_hien': { color: 'orange', text: 'Đang kiểm' },
      'hoan_thanh': { color: 'green', text: 'Hoàn thành' },
      'huy': { color: 'red', text: 'Hủy' }
    };
    const config = statusConfig[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // Calculate progress
  const progress = stockCheck.tong_san_pham > 0 
    ? Math.round((stockCheck.san_pham_da_kiem / stockCheck.tong_san_pham) * 100)
    : 0;

  // Handle edit item
  const handleEditItem = (item) => {
    setEditingItem(item.id);
    form.setFieldsValue({
      [`so_luong_thuc_te_${item.id}`]: item.so_luong_thuc_te,
      [`ghi_chu_${item.id}`]: item.ghi_chu
    });
  };

  // Handle save item
  const handleSaveItem = async (item) => {
    try {
      const values = form.getFieldsValue();
      const so_luong_thuc_te = values[`so_luong_thuc_te_${item.id}`];
      const ghi_chu = values[`ghi_chu_${item.id}`];

      await updateItemMutation.mutateAsync({
        stockCheckId: id,
        itemId: item.id,
        so_luong_thuc_te,
        ghi_chu
      });

      setEditingItem(null);
      refetch();
    } catch (error) {
      // Error handled by mutation
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingItem(null);
    form.resetFields();
  };

  // Handle remove product
  const handleRemoveProduct = async (itemId) => {
    try {
      await removeProductMutation.mutateAsync({
        stockCheckId: id,
        itemId
      });
      refetch();
    } catch (error) {
      // Error handled by mutation
    }
  };

  // Handle add products success
  const handleAddProductsSuccess = () => {
    setShowAddProductsModal(false);
    refetch();
  };

  // Filter available products for modal (API already returns products not in stock check)
  console.log('🛒 availableProducts type:', typeof availableProducts);
  console.log('🛒 availableProducts value:', availableProducts);
  console.log('🛒 availableProducts isArray:', Array.isArray(availableProducts));

  const availableProductsArray = Array.isArray(availableProducts) ? availableProducts : [];
  const filteredProducts = availableProductsArray.filter(product =>
    !modalSearchText ||
    product.ten_san_pham?.toLowerCase().includes(modalSearchText.toLowerCase()) ||
    product.ten_phien_ban?.toLowerCase().includes(modalSearchText.toLowerCase()) ||
    product.ma_sku?.toLowerCase().includes(modalSearchText.toLowerCase())
  );

  // Show loading if data is not ready
  if (detailsLoading && !stockCheck.id) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <div>Đang tải...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        marginBottom: '24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        padding: '16px 24px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/warehouses/stock-check')}
          >
            Chi tiết phiếu kiểm kê
          </Button>
          <Title level={4} style={{ margin: 0 }}>
            {stockCheck.ma_kiem_ke} - {stockCheck.ten_kiem_ke}
          </Title>
          {getStockCheckStatusTag(stockCheck.trang_thai)}
        </Space>
        <Space>
          {!isEditing ? (
            <Button 
              icon={<EditOutlined />}
              onClick={() => setIsEditing(true)}
              disabled={stockCheck.trang_thai === 'hoan_thanh'}
            >
              Chỉnh sửa
            </Button>
          ) : (
            <>
              <Button onClick={() => setIsEditing(false)}>
                Hủy
              </Button>
              <Button 
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveStockCheck}
                loading={updateStockCheckMutation.isLoading}
              >
                Lưu
              </Button>
            </>
          )}
          {(stockCheck.trang_thai === 'dang_kiem' || stockCheck.trang_thai === 'dang_thuc_hien') && progress === 100 && (
            <Popconfirm
              title="Cân bằng kho"
              description="Bạn có chắc chắn muốn cân bằng kho? Hành động này sẽ cập nhật tồn kho theo số liệu thực tế."
              onConfirm={handleCompleteStockCheck}
              okText="Cân bằng"
              cancelText="Hủy"
              icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
            >
              <Button
                type="primary"
                icon={<CheckOutlined />}
                loading={completeStockCheckMutation.isLoading}
              >
                Cân bằng kho
              </Button>
            </Popconfirm>
          )}
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
      >
        {/* Form thông tin phiếu */}
        <Row gutter={24} style={{ marginBottom: 24 }}>
          {/* Cột trái - Thông tin phiếu */}
          <Col span={12}>
            <Card title="Thông tin phiếu" style={{ height: '100%' }}>
              <Form.Item
                name="ten_kiem_ke"
                label="Tên phiếu kiểm"
                rules={[{ required: true, message: 'Vui lòng nhập tên phiếu kiểm' }]}
              >
                <Input placeholder="Nhập tên phiếu kiểm" disabled={!isEditing} />
              </Form.Item>

              <Form.Item
                name="kho_hang_id"
                label="Kho"
                rules={[{ required: true, message: 'Vui lòng chọn kho' }]}
              >
                <Select placeholder="Chọn kho" disabled={!isEditing}>
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.ten_kho}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label="Mã phiếu"
              >
                <Input value={stockCheck.ma_kiem_ke} disabled />
              </Form.Item>
            </Card>
          </Col>

          {/* Cột phải - Thông tin bổ sung */}
          <Col span={12}>
            <Card title="Thông tin bổ sung" style={{ height: '100%' }}>
              <Form.Item
                name="nguoi_kiem_ke"
                label="Nhân viên kiểm"
                rules={[{ required: true, message: 'Vui lòng nhập tên nhân viên' }]}
              >
                <Input placeholder="Nhập tên nhân viên kiểm" disabled={!isEditing} />
              </Form.Item>

              <Form.Item
                name="ngay_bat_dau"
                label="Ngày bắt đầu"
                rules={[{ required: true, message: 'Vui lòng chọn ngày' }]}
              >
                <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" disabled={!isEditing} />
              </Form.Item>

              <Form.Item
                name="ngay_ket_thuc"
                label="Ngày kết thúc"
              >
                <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" disabled={!isEditing} />
              </Form.Item>

              <Form.Item
                name="ghi_chu"
                label="Ghi chú"
              >
                <TextArea rows={2} placeholder="Nhập ghi chú" disabled={!isEditing} />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        {/* Bảng sản phẩm */}
        <Card
          title="Thông tin sản phẩm"
          extra={
            <Space>
              <Input
                placeholder="Tìm kiếm sản phẩm..."
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 200 }}
                allowClear
              />
              {stockCheck.trang_thai !== 'hoan_thanh' && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowAddProductsModal(true)}
                >
                  Thêm sản phẩm vào phiếu
                </Button>
              )}
            </Space>
          }
        >
          <Table
            columns={[
              {
                title: 'STT',
                key: 'stt',
                width: 60,
                align: 'center',
                render: (_, __, index) => (currentPage - 1) * pageSize + index + 1
              },
              {
                title: 'Ảnh',
                dataIndex: 'anh',
                key: 'anh',
                width: 80,
                align: 'center',
                render: (anh, record) => (
                  <Image
                    src={anh || '/placeholder-image.png'}
                    alt={record.ten_san_pham}
                    width={50}
                    height={50}
                    style={{ objectFit: 'cover', borderRadius: 4 }}
                    fallback="/placeholder-image.png"
                  />
                )
              },
              {
                title: 'Tên sản phẩm',
                key: 'product',
                width: 250,
                render: (_, record) => (
                  <div>
                    <div style={{ fontWeight: 500 }}>{record.ten_san_pham}</div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {record.ten_phien_ban} - {record.ma_sku}
                    </Text>
                  </div>
                )
              },
              {
                title: 'Đơn vị',
                dataIndex: 'don_vi_tinh',
                key: 'don_vi_tinh',
                width: 80,
                align: 'center'
              },
              {
                title: 'Tồn hệ thống',
                dataIndex: 'so_luong_he_thong',
                key: 'so_luong_he_thong',
                width: 120,
                align: 'center',
                render: (value) => value?.toLocaleString('vi-VN') || 0
              },
              {
                title: 'Thực tế',
                key: 'so_luong_thuc_te',
                width: 120,
                align: 'center',
                render: (_, record) => {
                  if (editingItem === record.id) {
                    return (
                      <Form.Item
                        name={`so_luong_thuc_te_${record.id}`}
                        style={{ margin: 0 }}
                      >
                        <InputNumber
                          min={0}
                          style={{ width: '100%' }}
                          placeholder="Nhập số lượng"
                        />
                      </Form.Item>
                    );
                  }
                  return record.so_luong_thuc_te !== null
                    ? record.so_luong_thuc_te?.toLocaleString('vi-VN')
                    : <Text type="secondary">Chưa kiểm</Text>;
                }
              },
              {
                title: 'Chênh lệch',
                key: 'chenh_lech',
                width: 120,
                align: 'center',
                render: (_, record) => {
                  if (record.so_luong_thuc_te === null) return '-';
                  const diff = record.so_luong_thuc_te - record.so_luong_he_thong;
                  return (
                    <Text style={{
                      color: diff > 0 ? '#52c41a' : diff < 0 ? '#ff4d4f' : '#666'
                    }}>
                      {diff > 0 ? '+' : ''}{diff?.toLocaleString('vi-VN')}
                    </Text>
                  );
                }
              },
              {
                title: 'Ghi chú',
                key: 'ghi_chu',
                width: 200,
                render: (_, record) => {
                  if (editingItem === record.id) {
                    return (
                      <Form.Item
                        name={`ghi_chu_${record.id}`}
                        style={{ margin: 0 }}
                      >
                        <Input placeholder="Nhập ghi chú" />
                      </Form.Item>
                    );
                  }
                  return record.ghi_chu || '-';
                }
              },
              {
                title: 'Thao tác',
                key: 'action',
                width: 150,
                align: 'center',
                render: (_, record) => {
                  if (stockCheck.trang_thai === 'hoan_thanh') {
                    return <Text type="secondary">Đã hoàn thành</Text>;
                  }

                  if (editingItem === record.id) {
                    return (
                      <Space>
                        <Button
                          type="primary"
                          size="small"
                          icon={<SaveOutlined />}
                          onClick={() => handleSaveItem(record)}
                          loading={updateItemMutation.isLoading}
                        >
                          Lưu
                        </Button>
                        <Button
                          size="small"
                          icon={<CloseOutlined />}
                          onClick={handleCancelEdit}
                        >
                          Hủy
                        </Button>
                      </Space>
                    );
                  }

                  return (
                    <Space>
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEditItem(record)}
                      >
                        Kiểm kê
                      </Button>
                      {record.so_luong_thuc_te === null && (
                        <Popconfirm
                          title="Xóa sản phẩm"
                          description="Bạn có chắc chắn muốn xóa sản phẩm này khỏi phiếu kiểm kê?"
                          onConfirm={() => handleRemoveProduct(record.id)}
                          okText="Xóa"
                          cancelText="Hủy"
                        >
                          <Button
                            type="text"
                            size="small"
                            icon={<DeleteOutlined />}
                            danger
                            loading={removeProductMutation.isLoading}
                          >
                            Xóa
                          </Button>
                        </Popconfirm>
                      )}
                    </Space>
                  );
                }
              }
            ]}
            dataSource={details}
            loading={detailsLoading}
            rowKey="id"
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: pagination.total || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} của ${total} sản phẩm`,
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size);
              },
            }}
            scroll={{ x: 1200 }}
            locale={{
              emptyText: (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Text type="secondary">
                    Chưa có sản phẩm nào được chọn để kiểm kê
                  </Text>
                  <br />
                  {stockCheck.trang_thai !== 'hoan_thanh' && (
                    <Button
                      type="link"
                      icon={<PlusOutlined />}
                      onClick={() => setShowAddProductsModal(true)}
                    >
                      Thêm sản phẩm
                    </Button>
                  )}
                </div>
              )
            }}
          />
        </Card>
      </Form>

      {/* Modal thêm sản phẩm */}
      <Modal
        title="Thêm sản phẩm vào phiếu kiểm kê"
        open={showAddProductsModal}
        onCancel={() => setShowAddProductsModal(false)}
        footer={null}
        width={800}
      >
        {!selectedWarehouseId ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Text type="secondary">
              Vui lòng chọn kho hàng trước khi thêm sản phẩm
            </Text>
          </div>
        ) : (
          <div>
            <Input
              placeholder="Tìm kiếm sản phẩm..."
              prefix={<SearchOutlined />}
              value={modalSearchText}
              onChange={(e) => setModalSearchText(e.target.value)}
              style={{ width: '100%', marginBottom: 16 }}
              allowClear
            />
            <Table
              columns={[
                {
                  title: 'Sản phẩm',
                  key: 'product',
                  render: (_, record) => (
                    <div>
                      <div style={{ fontWeight: 500 }}>{record.ten_san_pham}</div>
                      <Text type="secondary">{record.ten_phien_ban}</Text>
                    </div>
                  )
                },
                {
                  title: 'Tồn kho',
                  dataIndex: 'so_luong_ton',
                  key: 'so_luong_ton',
                  align: 'center',
                  render: (value) => value?.toLocaleString('vi-VN') || 0
                },
                {
                  title: 'Thao tác',
                  key: 'action',
                  align: 'center',
                  render: (_, record) => (
                    <Button
                      type="primary"
                      size="small"
                      onClick={async () => {
                        try {
                          await addProductMutation.mutateAsync({
                            id,
                            phien_ban_san_pham_ids: [record.id]
                          });
                          handleAddProductsSuccess();
                        } catch (error) {
                          // Error handled by mutation
                        }
                      }}
                      loading={addProductMutation.isLoading}
                    >
                      Thêm
                    </Button>
                  )
                }
              ]}
              dataSource={filteredProducts}
              loading={productsLoading}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default StockCheckEdit;
