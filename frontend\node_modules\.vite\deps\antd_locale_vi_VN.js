import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// node_modules/@babel/runtime/helpers/interopRequireDefault.js
var require_interopRequireDefault = __commonJS({
  "node_modules/@babel/runtime/helpers/interopRequireDefault.js"(exports, module) {
    function _interopRequireDefault(e) {
      return e && e.__esModule ? e : {
        "default": e
      };
    }
    module.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/rc-pagination/lib/locale/vi_VN.js
var require_vi_VN = __commonJS({
  "node_modules/rc-pagination/lib/locale/vi_VN.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var locale = {
      // Options
      items_per_page: "/ trang",
      jump_to: "Đến",
      jump_to_confirm: "xác nhận",
      page: "Trang",
      // Pagination
      prev_page: "Trang Trước",
      next_page: "Trang Kế",
      prev_5: "Về 5 Trang Trước",
      next_5: "Đến 5 Trang Kế",
      prev_3: "Về 3 Trang Trước",
      next_3: "Đến 3 Trang Kế",
      page_size: "kích thước trang"
    };
    var _default = exports.default = locale;
  }
});

// node_modules/@babel/runtime/helpers/typeof.js
var require_typeof = __commonJS({
  "node_modules/@babel/runtime/helpers/typeof.js"(exports, module) {
    function _typeof(o) {
      "@babel/helpers - typeof";
      return module.exports = _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
        return typeof o2;
      } : function(o2) {
        return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
      }, module.exports.__esModule = true, module.exports["default"] = module.exports, _typeof(o);
    }
    module.exports = _typeof, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/toPrimitive.js
var require_toPrimitive = __commonJS({
  "node_modules/@babel/runtime/helpers/toPrimitive.js"(exports, module) {
    var _typeof = require_typeof()["default"];
    function toPrimitive(t, r) {
      if ("object" != _typeof(t) || !t) return t;
      var e = t[Symbol.toPrimitive];
      if (void 0 !== e) {
        var i = e.call(t, r || "default");
        if ("object" != _typeof(i)) return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return ("string" === r ? String : Number)(t);
    }
    module.exports = toPrimitive, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/toPropertyKey.js
var require_toPropertyKey = __commonJS({
  "node_modules/@babel/runtime/helpers/toPropertyKey.js"(exports, module) {
    var _typeof = require_typeof()["default"];
    var toPrimitive = require_toPrimitive();
    function toPropertyKey(t) {
      var i = toPrimitive(t, "string");
      return "symbol" == _typeof(i) ? i : i + "";
    }
    module.exports = toPropertyKey, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/defineProperty.js
var require_defineProperty = __commonJS({
  "node_modules/@babel/runtime/helpers/defineProperty.js"(exports, module) {
    var toPropertyKey = require_toPropertyKey();
    function _defineProperty(e, r, t) {
      return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: true,
        configurable: true,
        writable: true
      }) : e[r] = t, e;
    }
    module.exports = _defineProperty, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/objectSpread2.js
var require_objectSpread2 = __commonJS({
  "node_modules/@babel/runtime/helpers/objectSpread2.js"(exports, module) {
    var defineProperty = require_defineProperty();
    function ownKeys(e, r) {
      var t = Object.keys(e);
      if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r2) {
          return Object.getOwnPropertyDescriptor(e, r2).enumerable;
        })), t.push.apply(t, o);
      }
      return t;
    }
    function _objectSpread2(e) {
      for (var r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), true).forEach(function(r2) {
          defineProperty(e, r2, t[r2]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r2) {
          Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
        });
      }
      return e;
    }
    module.exports = _objectSpread2, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/rc-picker/lib/locale/common.js
var require_common = __commonJS({
  "node_modules/rc-picker/lib/locale/common.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.commonLocale = void 0;
    var commonLocale = exports.commonLocale = {
      yearFormat: "YYYY",
      dayFormat: "D",
      cellMeridiemFormat: "A",
      monthBeforeYear: true
    };
  }
});

// node_modules/rc-picker/lib/locale/vi_VN.js
var require_vi_VN2 = __commonJS({
  "node_modules/rc-picker/lib/locale/vi_VN.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _objectSpread2 = _interopRequireDefault(require_objectSpread2());
    var _common = require_common();
    var locale = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _common.commonLocale), {}, {
      locale: "vi_VN",
      today: "Hôm nay",
      now: "Bây giờ",
      backToToday: "Trở về hôm nay",
      ok: "OK",
      clear: "Xóa",
      week: "Tuần",
      month: "Tháng",
      year: "Năm",
      timeSelect: "Chọn thời gian",
      dateSelect: "Chọn ngày",
      weekSelect: "Chọn tuần",
      monthSelect: "Chọn tháng",
      yearSelect: "Chọn năm",
      decadeSelect: "Chọn thập kỷ",
      dateFormat: "D/M/YYYY",
      dateTimeFormat: "D/M/YYYY HH:mm:ss",
      previousMonth: "Tháng trước (PageUp)",
      nextMonth: "Tháng sau (PageDown)",
      previousYear: "Năm trước (Control + left)",
      nextYear: "Năm sau (Control + right)",
      previousDecade: "Thập kỷ trước",
      nextDecade: "Thập kỷ sau",
      previousCentury: "Thế kỷ trước",
      nextCentury: "Thế kỷ sau"
    });
    var _default = exports.default = locale;
  }
});

// node_modules/antd/lib/time-picker/locale/vi_VN.js
var require_vi_VN3 = __commonJS({
  "node_modules/antd/lib/time-picker/locale/vi_VN.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var locale = {
      placeholder: "Chọn thời gian",
      rangePlaceholder: ["Bắt đầu", "Kết thúc"]
    };
    var _default = exports.default = locale;
  }
});

// node_modules/antd/lib/date-picker/locale/vi_VN.js
var require_vi_VN4 = __commonJS({
  "node_modules/antd/lib/date-picker/locale/vi_VN.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _vi_VN = _interopRequireDefault(require_vi_VN2());
    var _vi_VN2 = _interopRequireDefault(require_vi_VN3());
    var locale = {
      lang: Object.assign({
        placeholder: "Chọn thời điểm",
        yearPlaceholder: "Chọn năm",
        quarterPlaceholder: "Chọn quý",
        monthPlaceholder: "Chọn tháng",
        weekPlaceholder: "Chọn tuần",
        rangePlaceholder: ["Ngày bắt đầu", "Ngày kết thúc"],
        rangeYearPlaceholder: ["Năm bắt đầu", "Năm kết thúc"],
        rangeQuarterPlaceholder: ["Quý bắt đầu", "Quý kết thúc"],
        rangeMonthPlaceholder: ["Tháng bắt đầu", "Tháng kết thúc"],
        rangeWeekPlaceholder: ["Tuần bắt đầu", "Tuần kết thúc"]
      }, _vi_VN.default),
      timePickerLocale: Object.assign({}, _vi_VN2.default)
    };
    var _default = exports.default = locale;
  }
});

// node_modules/antd/lib/calendar/locale/vi_VN.js
var require_vi_VN5 = __commonJS({
  "node_modules/antd/lib/calendar/locale/vi_VN.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _vi_VN = _interopRequireDefault(require_vi_VN4());
    var _default = exports.default = _vi_VN.default;
  }
});

// node_modules/antd/lib/locale/vi_VN.js
var require_vi_VN6 = __commonJS({
  "node_modules/antd/lib/locale/vi_VN.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _vi_VN = _interopRequireDefault(require_vi_VN());
    var _vi_VN2 = _interopRequireDefault(require_vi_VN5());
    var _vi_VN3 = _interopRequireDefault(require_vi_VN4());
    var _vi_VN4 = _interopRequireDefault(require_vi_VN3());
    var typeTemplate = "${label} không phải kiểu ${type} hợp lệ";
    var localeValues = {
      locale: "vi",
      Pagination: _vi_VN.default,
      DatePicker: _vi_VN3.default,
      TimePicker: _vi_VN4.default,
      Calendar: _vi_VN2.default,
      global: {
        placeholder: "Vui lòng chọn",
        close: "Đóng"
      },
      Table: {
        filterTitle: "Bộ lọc",
        filterConfirm: "Đồng ý",
        filterReset: "Bỏ lọc",
        filterEmptyText: "Không có bộ lọc",
        filterCheckAll: "Chọn tất cả",
        filterSearchPlaceholder: "Tìm kiếm bộ lọc",
        emptyText: "Trống",
        selectAll: "Chọn tất cả",
        selectInvert: "Chọn ngược lại",
        selectNone: "Bỏ chọn tất cả",
        selectionAll: "Chọn tất cả",
        sortTitle: "Sắp xếp",
        expand: "Mở rộng dòng",
        collapse: "Thu gọn dòng",
        triggerDesc: "Nhấp để sắp xếp giảm dần",
        triggerAsc: "Nhấp để sắp xếp tăng dần",
        cancelSort: "Nhấp để hủy sắp xếp"
      },
      Tour: {
        Next: "Tiếp",
        Previous: "Trước",
        Finish: "Hoàn thành"
      },
      Modal: {
        okText: "Đồng ý",
        cancelText: "Hủy",
        justOkText: "OK"
      },
      Popconfirm: {
        okText: "Đồng ý",
        cancelText: "Hủy"
      },
      Transfer: {
        titles: ["", ""],
        searchPlaceholder: "Tìm ở đây",
        itemUnit: "mục",
        itemsUnit: "mục",
        remove: "Gỡ bỏ",
        selectCurrent: "Chọn trang hiện tại",
        removeCurrent: "Gỡ bỏ trang hiện tại",
        selectAll: "Chọn tất cả",
        removeAll: "Gỡ bỏ tất cả",
        selectInvert: "Đảo ngược trang hiện tại"
      },
      Upload: {
        uploading: "Đang tải lên...",
        removeFile: "Gỡ bỏ tập tin",
        uploadError: "Lỗi tải lên",
        previewFile: "Xem trước tập tin",
        downloadFile: "Tải tập tin"
      },
      Empty: {
        description: "Trống"
      },
      Icon: {
        icon: "icon"
      },
      Text: {
        edit: "Chỉnh sửa",
        copy: "Sao chép",
        copied: "Đã sao chép",
        expand: "Mở rộng"
      },
      Form: {
        optional: "(Tùy chọn)",
        defaultValidateMessages: {
          default: "${label} không đáp ứng điều kiện quy định",
          required: "Hãy nhập thông tin cho trường ${label}",
          enum: "${label} phải có giá trị nằm trong tập [${enum}]",
          whitespace: "${label} không được chứa khoảng trắng",
          date: {
            format: "${label} sai định dạng ngày tháng",
            parse: "Không thể chuyển ${label} sang kiểu Ngày tháng",
            invalid: "${label} không phải giá trị Ngày tháng hợp lệ"
          },
          types: {
            string: typeTemplate,
            method: typeTemplate,
            array: typeTemplate,
            object: typeTemplate,
            number: typeTemplate,
            date: typeTemplate,
            boolean: typeTemplate,
            integer: typeTemplate,
            float: typeTemplate,
            regexp: typeTemplate,
            email: typeTemplate,
            url: typeTemplate,
            hex: typeTemplate
          },
          string: {
            len: "${label} phải dài đúng ${len} ký tự",
            min: "Độ dài tối thiểu trường ${label} là ${min} ký tự",
            max: "Độ dài tối đa trường ${label} là ${max} ký tự",
            range: "Độ dài trường ${label} phải từ ${min} đến ${max} ký tự"
          },
          number: {
            len: "${label} phải bằng ${len}",
            min: "${label} phải lớn hơn hoặc bằng ${min}",
            max: "${label} phải nhỏ hơn hoặc bằng ${max}",
            range: "${label} phải nằm trong khoảng ${min}-${max}"
          },
          array: {
            len: "Mảng ${label} phải có ${len} phần tử ",
            min: "Mảng ${label} phải chứa tối thiểu ${min} phần tử ",
            max: "Mảng ${label} phải chứa tối đa ${max} phần tử ",
            range: "Mảng ${label} phải chứa từ ${min}-${max} phần tử"
          },
          pattern: {
            mismatch: "${label} không thỏa mãn mẫu kiểm tra ${pattern}"
          }
        }
      },
      Image: {
        preview: "Xem trước"
      },
      QRCode: {
        expired: "Mã QR hết hạn",
        refresh: "Làm mới"
      }
    };
    var _default = exports.default = localeValues;
  }
});

// node_modules/antd/locale/vi_VN.js
var require_vi_VN7 = __commonJS({
  "node_modules/antd/locale/vi_VN.js"(exports, module) {
    module.exports = require_vi_VN6();
  }
});
export default require_vi_VN7();
//# sourceMappingURL=antd_locale_vi_VN.js.map
