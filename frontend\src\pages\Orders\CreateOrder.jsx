import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Button,
  Form,
  Typography,
  Space,
  Divider,
  Table,
  InputNumber,
  Checkbox,
  message,
  Empty,
  Spin,
  Switch
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  ShoppingCartOutlined,
  TruckOutlined,
  ShopOutlined,
  HomeOutlined,
  GiftOutlined
} from '@ant-design/icons';
import { useCustomers } from '../../hooks/useCustomers';
import { useAllProductVariants } from '../../hooks/useProducts';
import { useCreateOrder } from '../../hooks/useOrders';
import { ORDER_STATUS, ORDER_STATUS_LABELS, DELIVERY_METHODS } from '../../constants/orderStatus';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CreateOrder = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  // States
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [customerSearch, setCustomerSearch] = useState('');
  const [productSearch, setProductSearch] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [discountType, setDiscountType] = useState('amount'); // 'amount' or 'percent'
  const [isStockCheck, setIsStockCheck] = useState(true);
  const [customerDropdownOpen, setCustomerDropdownOpen] = useState(false);
  const [productDropdownOpen, setProductDropdownOpen] = useState(false);
  const [deliveryMethod, setDeliveryMethod] = useState(DELIVERY_METHODS.STORE_PICKUP);
  const [highlightedCustomerIndex, setHighlightedCustomerIndex] = useState(-1);
  const [highlightedProductIndex, setHighlightedProductIndex] = useState(-1);
  const [manualTotal, setManualTotal] = useState(null); // Cho phép nhập tổng tiền thủ công
  const [isManualTotalMode, setIsManualTotalMode] = useState(false); // Chế độ nhập thủ công
  const [depositAmount, setDepositAmount] = useState(0); // Tiền cọc
  const [codAmount, setCodAmount] = useState(0); // Tiền COD (CTV có thể bán giá cao hơn)

  // API hooks
  const { data: customers = [], isLoading: customersLoading } = useCustomers({
    search: customerSearch || '', // Always pass empty string if no search
    limit: 20
  });

  const { data: productVariants = [], isLoading: productsLoading, error: productsError, refetch: refetchProducts } = useAllProductVariants({
    search: productSearch,
    limit: 50
  });

  // Debug log
  console.log('🔧 CreateOrder - Product variants:', {
    productVariants,
    count: productVariants?.length,
    isLoading: productsLoading,
    error: productsError,
    search: productSearch
  });

  // Effect để refetch products khi component mount
  useEffect(() => {
    console.log('🔧 CreateOrder mounted, refetching products...');
    refetchProducts();
  }, [refetchProducts]);

  const createOrderMutation = useCreateOrder();

  // Calculate totals
  const subtotal = selectedProducts.reduce((sum, item) =>
    sum + (item.so_luong * item.gia_ban), 0
  );

  const discountAmount = form.getFieldValue('giam_gia') || 0;
  const finalDiscount = discountType === 'percent'
    ? (subtotal * discountAmount / 100)
    : discountAmount;

  // Sử dụng manual total nếu đang ở chế độ thủ công, ngược lại tính tự động
  const total = isManualTotalMode && manualTotal !== null ? manualTotal : (subtotal - finalDiscount);

  // Tính công nợ: Tiền sản phẩm - Tiền cọc - Tiền COD
  // COD có thể lớn hơn tiền sản phẩm (CTV bán giá cao hơn)
  const debtAmount = total - depositAmount - codAmount;

  // Handle customer selection
  const handleCustomerSelect = (customer) => {
    setSelectedCustomer(customer);
    setCustomerSearch(customer?.ho_ten || customer?.ten || '');
    setCustomerDropdownOpen(false);
    form.setFieldsValue({
      khach_hang_id: customer?.id,
      ten_khach_hang: customer?.ho_ten || customer?.ten,
      so_dien_thoai: customer?.so_dien_thoai,
      dia_chi: customer?.dia_chi
    });
  };

  // Handle customer search change
  const handleCustomerSearchChange = (value) => {
    setCustomerSearch(value);
    setHighlightedCustomerIndex(-1); // Reset highlight khi search

    // Nếu user xóa hết text hoặc thay đổi text khác với khách hàng đã chọn
    if (!value || (selectedCustomer && value !== (selectedCustomer?.ho_ten || selectedCustomer?.ten || ''))) {
      setSelectedCustomer(null); // Reset selected customer
      form.setFieldsValue({
        khach_hang_id: null,
        ten_khach_hang: '',
        so_dien_thoai: '',
        dia_chi: ''
      });
    }

    if (!customerDropdownOpen) {
      setCustomerDropdownOpen(true);
    }
  };

  // Handle customer dropdown focus
  const handleCustomerFocus = () => {
    // Chỉ mở dropdown nếu chưa chọn khách hàng hoặc đang search
    if (!selectedCustomer || customerSearch !== (selectedCustomer?.ho_ten || selectedCustomer?.ten || '')) {
      setCustomerDropdownOpen(true);
      setHighlightedCustomerIndex(-1); // Reset highlight khi focus
    }
  };

  // Handle customer dropdown blur
  const handleCustomerBlur = () => {
    // Delay hiding dropdown to allow selection
    setTimeout(() => {
      setCustomerDropdownOpen(false);
      setHighlightedCustomerIndex(-1);
    }, 300); // Tăng delay lên 300ms để đảm bảo click event được xử lý
  };

  // Handle keyboard navigation for customer dropdown
  const handleCustomerKeyDown = (e) => {
    if (!customerDropdownOpen) return;

    const filteredCustomers = customers.filter(customer => {
      if (!customerSearch) return true;
      return (
        customer.ho_ten?.toLowerCase().includes(customerSearch.toLowerCase()) ||
        customer.ten?.toLowerCase().includes(customerSearch.toLowerCase()) ||
        customer.so_dien_thoai?.includes(customerSearch) ||
        customer.ma_khach_hang?.toLowerCase().includes(customerSearch.toLowerCase())
      );
    });

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedCustomerIndex(prev =>
          prev < filteredCustomers.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedCustomerIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedCustomerIndex >= 0 && filteredCustomers[highlightedCustomerIndex]) {
          handleCustomerSelect(filteredCustomers[highlightedCustomerIndex]);
        }
        break;
      case 'Escape':
        setCustomerDropdownOpen(false);
        setHighlightedCustomerIndex(-1);
        break;
    }
  };

  // Handle product selection
  const handleProductSelect = (product) => {
    if (!product) return;

    // Check if product already exists
    const existingIndex = selectedProducts.findIndex(p => p.id === product.id);
    if (existingIndex >= 0) {
      // Increase quantity
      const newProducts = [...selectedProducts];
      newProducts[existingIndex].so_luong += 1;
      setSelectedProducts(newProducts);
    } else {
      // Add new product
      const newProduct = {
        id: product.id,
        ten_san_pham: product.ten_san_pham,
        ten_phien_ban: product.ten_phien_ban,
        ma_sku: product.ma_sku,
        anh: product.anh,
        gia_ban: product.gia_ban_le || 0,
        so_luong: 1,
        ton_kho: product.ton_kho || 0
      };
      setSelectedProducts([...selectedProducts, newProduct]);
    }
    setProductSearch('');
    setProductDropdownOpen(false);
  };

  // Handle product search change
  const handleProductSearchChange = (value) => {
    setProductSearch(value);
    setHighlightedProductIndex(-1); // Reset highlight khi search
    if (!productDropdownOpen) {
      setProductDropdownOpen(true);
    }
  };

  // Handle product dropdown focus
  const handleProductFocus = () => {
    setProductDropdownOpen(true);
    setHighlightedProductIndex(-1); // Reset highlight khi focus
  };

  // Handle product dropdown blur
  const handleProductBlur = () => {
    // Delay hiding dropdown to allow selection
    setTimeout(() => {
      setProductDropdownOpen(false);
      setHighlightedProductIndex(-1);
    }, 300); // Tăng delay lên 300ms để đảm bảo click event được xử lý
  };

  // Handle keyboard navigation for product dropdown
  const handleProductKeyDown = (e) => {
    if (!productDropdownOpen) return;

    const filteredProducts = productVariants
      .filter(product => {
        if (!productSearch) return true;
        const searchLower = productSearch.toLowerCase();
        return (
          product.ten_san_pham?.toLowerCase().includes(searchLower) ||
          product.ten_phien_ban?.toLowerCase().includes(searchLower) ||
          product.ma_sku?.toLowerCase().includes(searchLower)
        );
      })
      .slice(0, 20);

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedProductIndex(prev =>
          prev < filteredProducts.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedProductIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedProductIndex >= 0 && filteredProducts[highlightedProductIndex]) {
          handleProductSelect(filteredProducts[highlightedProductIndex]);
        }
        break;
      case 'Escape':
        setProductDropdownOpen(false);
        setHighlightedProductIndex(-1);
        break;
    }
  };

  // Handle quantity change
  const handleQuantityChange = (productId, quantity) => {
    const newProducts = selectedProducts.map(product => 
      product.id === productId 
        ? { ...product, so_luong: quantity || 0 }
        : product
    );
    setSelectedProducts(newProducts);
  };

  // Handle price change
  const handlePriceChange = (productId, price) => {
    const newProducts = selectedProducts.map(product => 
      product.id === productId 
        ? { ...product, gia_ban: price || 0 }
        : product
    );
    setSelectedProducts(newProducts);
  };

  // Remove product
  const handleRemoveProduct = (productId) => {
    setSelectedProducts(selectedProducts.filter(p => p.id !== productId));
  };

  // Handle manual total mode toggle
  const handleManualTotalToggle = (checked) => {
    setIsManualTotalMode(checked);
    if (checked) {
      // Khi chuyển sang chế độ thủ công, set giá trị hiện tại làm mặc định
      const currentTotal = subtotal - finalDiscount;
      setManualTotal(currentTotal);
      console.log('🔄 Switched to manual mode, current total:', currentTotal);
    } else {
      // Khi tắt chế độ thủ công, reset về null
      setManualTotal(null);
      console.log('🔄 Switched to auto mode');
    }
  };

  // Handle manual total change
  const handleManualTotalChange = (value) => {
    const newValue = value || 0;
    setManualTotal(newValue);
    console.log('💰 Manual total changed to:', newValue);
  };

  // Handle form submit
  const handleSubmit = async (values) => {
    try {
      if (selectedProducts.length === 0) {
        message.error('Vui lòng chọn ít nhất một sản phẩm');
        return;
      }

      if (!selectedCustomer) {
        message.error('Vui lòng chọn khách hàng');
        return;
      }

      const orderData = {
        khach_hang_id: selectedCustomer.id,
        ngay_dat_hang: values.ngay_dat_hang ? values.ngay_dat_hang.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        trang_thai: values.trang_thai || ORDER_STATUS.CHO_XU_LY,
        ghi_chu: values.ghi_chu || '',
        phuong_thuc_giao_hang: deliveryMethod,
        chi_tiet_don_hang: selectedProducts.map(product => ({
          phien_ban_san_pham_id: product.id,
          so_luong: product.so_luong,
          gia_ban: product.gia_ban,
          thanh_tien: product.so_luong * product.gia_ban
        })),
        tong_tien_hang: subtotal,
        giam_gia: finalDiscount,
        tong_thanh_toan: total, // Đây là tổng tiền cuối cùng (manual hoặc auto)
        manual_total_amount: isManualTotalMode ? manualTotal : null, // Giá trị manual total gốc
        is_manual_total: isManualTotalMode, // Đánh dấu là tổng tiền thủ công
        loai_giam_gia: discountType,
        tien_coc: depositAmount, // Tiền cọc
        tien_cod: codAmount, // Tiền COD (CTV có thể bán giá cao hơn)
        cong_no: debtAmount // Công nợ = Tiền sản phẩm - Tiền cọc - Tiền COD
      };

      console.log('📋 Creating order with data:', orderData);
      console.log('💰 Manual total debug:', {
        isManualTotalMode,
        manualTotal,
        total,
        subtotal,
        finalDiscount
      });

      const result = await createOrderMutation.mutateAsync(orderData);
      console.log('📋 Order creation result:', result);

      if (result?.success) {
        message.success('Tạo đơn hàng thành công!');
        navigate('/orders');
      } else {
        throw new Error(result?.message || 'Không thể tạo đơn hàng');
      }
    } catch (error) {
      console.error('❌ Error creating order:', error);
      message.error(error.message || 'Có lỗi xảy ra khi tạo đơn hàng');
    }
  };

  // Product table columns
  const productColumns = [
    {
      title: 'STT',
      key: 'stt',
      width: 50,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Sản phẩm',
      key: 'product',
      width: 300,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <img 
            src={record.anh || '/placeholder-image.png'} 
            alt={record.ten_san_pham}
            style={{ width: 40, height: 40, objectFit: 'cover', borderRadius: 4 }}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{record.ten_san_pham}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.ten_phien_ban} - {record.ma_sku}
            </Text>
            {isStockCheck && (
              <div>
                <Text type="secondary" style={{ fontSize: 11 }}>
                  Tồn: {record.ton_kho}
                </Text>
                {record.so_luong > record.ton_kho && (
                  <Text type="danger" style={{ fontSize: 11, marginLeft: 8 }}>
                    Vượt tồn kho!
                  </Text>
                )}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Số lượng',
      key: 'so_luong',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <InputNumber
          min={1}
          value={record.so_luong}
          onChange={(value) => handleQuantityChange(record.id, value)}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Đơn giá',
      key: 'gia_ban',
      width: 120,
      align: 'right',
      render: (_, record) => (
        <InputNumber
          min={0}
          value={record.gia_ban}
          onChange={(value) => handlePriceChange(record.id, value)}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\$\s?|(,*)/g, '')}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Thành tiền',
      key: 'thanh_tien',
      width: 120,
      align: 'right',
      render: (_, record) => (
        <Text strong>
          {(record.so_luong * record.gia_ban).toLocaleString('vi-VN')}đ
        </Text>
      )
    },
    {
      title: '',
      key: 'action',
      width: 50,
      align: 'center',
      render: (_, record) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveProduct(record.id)}
          size="small"
        />
      )
    }
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        marginBottom: '24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Title level={3} style={{ margin: 0 }}>
          <ShoppingCartOutlined style={{ marginRight: 8 }} />
          Tạo đơn hàng
        </Title>
        <Space>
          <Button onClick={() => navigate('/orders')}>
            Hủy
          </Button>
          <Button
            type="primary"
            onClick={() => form.submit()}
            disabled={selectedProducts.length === 0}
            loading={createOrderMutation.isLoading}
          >
            Tạo đơn hàng
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          ngay_dat_hang: dayjs(),
          trang_thai: 'cho_xu_ly',
          giam_gia: 0
        }}
      >
        <Row gutter={24}>
          {/* Left Column */}
          <Col span={16}>
            {/* Customer Information */}
            <Card
              title="Thông tin khách hàng"
              style={{ marginBottom: 24 }}
            >
              <div style={{ position: 'relative' }}>
                <Input
                  value={customerSearch}
                  onChange={(e) => handleCustomerSearchChange(e.target.value)}
                  onFocus={handleCustomerFocus}
                  onBlur={handleCustomerBlur}
                  onKeyDown={handleCustomerKeyDown}
                  placeholder={selectedCustomer ? "Khách hàng đã chọn - nhập để tìm khách hàng khác" : "Tìm theo tên, SĐT, mã khách hàng... (F4)"}
                  prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
                  style={{
                    marginBottom: 16,
                    backgroundColor: selectedCustomer ? '#f6ffed' : 'white',
                    borderColor: selectedCustomer ? '#52c41a' : '#d9d9d9'
                  }}
                />

                {customerDropdownOpen && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    right: 0,
                    backgroundColor: 'white',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                    zIndex: 1000,
                    maxHeight: '300px',
                    overflowY: 'auto'
                  }}>
                    {/* Add new customer button */}
                    <div
                      style={{
                        padding: '12px 16px',
                        borderBottom: '1px solid #f0f0f0',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 8,
                        color: '#1890ff'
                      }}
                      onMouseDown={(e) => {
                        e.preventDefault(); // Ngăn blur event
                        message.info('Tính năng thêm khách hàng mới sẽ được phát triển');
                        setCustomerDropdownOpen(false);
                      }}
                    >
                      <PlusOutlined />
                      <Text style={{ color: '#1890ff' }}>Thêm mới khách hàng</Text>
                    </div>

                    {/* Customer list */}
                    {customers
                      .filter(customer => {
                        if (!customerSearch) return true; // Show all customers when no search
                        return (
                          customer.ho_ten?.toLowerCase().includes(customerSearch.toLowerCase()) ||
                          customer.ten?.toLowerCase().includes(customerSearch.toLowerCase()) ||
                          customer.so_dien_thoai?.includes(customerSearch) ||
                          customer.ma_khach_hang?.toLowerCase().includes(customerSearch.toLowerCase())
                        );
                      })
                      .map((customer, index) => (
                        <div
                          key={customer.id}
                          style={{
                            padding: '12px 16px',
                            cursor: 'pointer',
                            borderBottom: '1px solid #f0f0f0',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 12,
                            backgroundColor: index === highlightedCustomerIndex ? '#e6f7ff' : 'white',
                            ':hover': {
                              backgroundColor: '#f5f5f5'
                            }
                          }}
                          onMouseDown={(e) => {
                            e.preventDefault(); // Ngăn blur event
                            handleCustomerSelect(customer);
                          }}
                          onMouseEnter={(e) => {
                            if (index !== highlightedCustomerIndex) {
                              e.target.style.backgroundColor = '#f5f5f5';
                            }
                            setHighlightedCustomerIndex(index);
                          }}
                          onMouseLeave={(e) => {
                            if (index !== highlightedCustomerIndex) {
                              e.target.style.backgroundColor = 'white';
                            }
                          }}
                        >
                          <div style={{
                            width: 32,
                            height: 32,
                            borderRadius: '50%',
                            backgroundColor: '#1890ff',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontSize: 14,
                            fontWeight: 500
                          }}>
                            {(customer.ho_ten || customer.ten)?.charAt(0)?.toUpperCase() || 'K'}
                          </div>
                          <div style={{ flex: 1 }}>
                            <div style={{ fontWeight: 500, marginBottom: 2 }}>
                              {customer.ho_ten || customer.ten || 'Khách hàng'}
                            </div>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {customer.so_dien_thoai} - {customer.ma_khach_hang}
                            </Text>
                          </div>
                        </div>
                      ))}

                    {customers.filter(customer => {
                      if (!customerSearch) return true;
                      return (
                        customer.ho_ten?.toLowerCase().includes(customerSearch.toLowerCase()) ||
                        customer.ten?.toLowerCase().includes(customerSearch.toLowerCase()) ||
                        customer.so_dien_thoai?.includes(customerSearch) ||
                        customer.ma_khach_hang?.toLowerCase().includes(customerSearch.toLowerCase())
                      );
                    }).length === 0 && customerSearch && (
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Text type="secondary">Không tìm thấy khách hàng</Text>
                      </div>
                    )}

                    {/* Show loading state */}
                    {customersLoading && (
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Text type="secondary">Đang tải danh sách khách hàng...</Text>
                      </div>
                    )}

                    {/* Show empty state when no customers */}
                    {!customersLoading && customers.length === 0 && (
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Text type="secondary">Chưa có khách hàng nào</Text>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Selected customer info */}
              {selectedCustomer && (
                <div style={{
                  padding: '16px',
                  backgroundColor: '#e6f7ff',
                  borderRadius: '8px',
                  marginBottom: 16,
                  border: '1px solid #91d5ff'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 8 }}>
                    <div style={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      backgroundColor: '#1890ff',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: 16,
                      fontWeight: 600
                    }}>
                      {(selectedCustomer.ho_ten || selectedCustomer.ten)?.charAt(0)?.toUpperCase() || 'K'}
                    </div>
                    <div style={{ flex: 1 }}>
                      <div style={{ fontWeight: 600, fontSize: 16, color: '#1890ff', marginBottom: 4 }}>
                        {selectedCustomer.ho_ten || selectedCustomer.ten}
                      </div>
                      <div style={{ display: 'flex', gap: 16 }}>
                        <Text type="secondary" style={{ fontSize: 13 }}>
                          📞 {selectedCustomer.so_dien_thoai}
                        </Text>
                        <Text type="secondary" style={{ fontSize: 13 }}>
                          🏷️ {selectedCustomer.ma_khach_hang}
                        </Text>
                      </div>
                    </div>
                    <Button
                      type="text"
                      size="small"
                      onClick={() => {
                        setSelectedCustomer(null);
                        setCustomerSearch('');
                        form.setFieldsValue({
                          khach_hang_id: null,
                          ten_khach_hang: '',
                          so_dien_thoai: '',
                          dia_chi: ''
                        });
                      }}
                    >
                      ✕
                    </Button>
                  </div>
                  {selectedCustomer.dia_chi && (
                    <Text type="secondary" style={{ fontSize: 13 }}>
                      📍 {selectedCustomer.dia_chi}
                    </Text>
                  )}
                </div>
              )}
            </Card>

            {/* Product Information */}
            <Card
              title="Thông tin sản phẩm"
              extra={
                <Space>
                  <Checkbox
                    checked={isStockCheck}
                    onChange={(e) => setIsStockCheck(e.target.checked)}
                  >
                    Tích đúng tồn
                  </Checkbox>
                  <Button type="link" style={{ color: '#1890ff', fontSize: 12 }}>
                    Kiểm tra tồn kho
                  </Button>
                  <Select defaultValue="F10" size="small" style={{ width: 80 }}>
                    <Option value="F10">(F10)</Option>
                  </Select>
                  <Button type="link" size="small">Giá bán lẻ</Button>
                </Space>
              }
            >
              <div style={{ position: 'relative', marginBottom: 16 }}>
                <Input
                  value={productSearch}
                  onChange={(e) => handleProductSearchChange(e.target.value)}
                  onFocus={handleProductFocus}
                  onBlur={handleProductBlur}
                  onKeyDown={handleProductKeyDown}
                  placeholder="Tìm theo tên, mã SKU, hoặc quét mã Barcode... (F3)"
                  prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
                  size="large"
                />

                {productDropdownOpen && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    right: 0,
                    backgroundColor: 'white',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.12)',
                    zIndex: 1000,
                    maxHeight: '400px',
                    overflowY: 'auto'
                  }}>
                    {/* Loading state */}
                    {productsLoading && (
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Spin size="small" />
                        <span style={{ marginLeft: 8 }}>Đang tải sản phẩm...</span>
                      </div>
                    )}

                    {/* Add new product button */}
                    {!productsLoading && (
                      <div
                        style={{
                          padding: '12px 16px',
                          borderBottom: '1px solid #f0f0f0',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 8,
                          color: '#1890ff',
                          backgroundColor: '#f6f8fa'
                        }}
                        onMouseDown={(e) => {
                          e.preventDefault(); // Ngăn blur event
                          message.info('Tính năng thêm sản phẩm mới sẽ được phát triển');
                          setProductDropdownOpen(false);
                        }}
                      >
                        <PlusOutlined />
                        <Text style={{ color: '#1890ff' }}>Thêm mới sản phẩm</Text>
                      </div>
                    )}

                    {/* Product list */}
                    {!productsLoading && (() => {
                      const filteredProducts = productVariants
                        .filter(product => {
                          if (!productSearch) return true; // Show all when no search
                          const searchLower = productSearch.toLowerCase();
                          return (
                            product.ten_san_pham?.toLowerCase().includes(searchLower) ||
                            product.ten_phien_ban?.toLowerCase().includes(searchLower) ||
                            product.ma_sku?.toLowerCase().includes(searchLower)
                          );
                        })
                        .slice(0, 20); // Limit to 20 items for performance

                      if (filteredProducts.length === 0) {
                        return (
                          <div style={{ padding: '20px', textAlign: 'center', color: '#999' }}>
                            {productSearch ?
                              `Không tìm thấy sản phẩm nào với từ khóa "${productSearch}"` :
                              'Chưa có sản phẩm nào trong hệ thống'
                            }
                          </div>
                        );
                      }

                      return filteredProducts.map((product, index) => (
                        <div
                          key={product.id}
                          style={{
                            padding: '12px 16px',
                            cursor: 'pointer',
                            borderBottom: '1px solid #f0f0f0',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 12,
                            backgroundColor: index === highlightedProductIndex ? '#e6f7ff' : 'white',
                            transition: 'background-color 0.2s'
                          }}
                          onMouseDown={(e) => {
                            e.preventDefault(); // Ngăn blur event
                            handleProductSelect(product);
                          }}
                          onMouseEnter={(e) => {
                            if (index !== highlightedProductIndex) {
                              e.currentTarget.style.backgroundColor = '#f5f5f5';
                            }
                            setHighlightedProductIndex(index);
                          }}
                          onMouseLeave={(e) => {
                            if (index !== highlightedProductIndex) {
                              e.currentTarget.style.backgroundColor = 'white';
                            }
                          }}
                        >
                          <img
                            src={product.anh || '/placeholder-image.png'}
                            alt={product.ten_san_pham}
                            style={{
                              width: 48,
                              height: 48,
                              objectFit: 'cover',
                              borderRadius: 6,
                              border: '1px solid #f0f0f0'
                            }}
                          />
                          <div style={{ flex: 1 }}>
                            <div style={{
                              fontWeight: 500,
                              marginBottom: 4,
                              fontSize: 14,
                              color: '#262626'
                            }}>
                              {product.ten_san_pham}
                            </div>
                            <Text type="secondary" style={{ fontSize: 12, display: 'block', marginBottom: 4 }}>
                              {product.ma_sku} - {product.ten_phien_ban}
                            </Text>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Text style={{
                                fontSize: 14,
                                color: '#1890ff',
                                fontWeight: 500
                              }}>
                                {product.gia_ban_le?.toLocaleString('vi-VN')}đ
                              </Text>
                              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                <Text style={{
                                  fontSize: 12,
                                  color: (product.ton_kho || 0) > 0 ? '#52c41a' : '#ff4d4f',
                                  fontWeight: 500
                                }}>
                                  Tồn: {product.ton_kho || 0}
                                </Text>
                                {(product.ton_kho || 0) > 0 && (
                                  <Text style={{
                                    fontSize: 12,
                                    color: '#52c41a'
                                  }}>
                                    Có thể bán: {product.ton_kho || 0}
                                  </Text>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ));
                    })()}

                    {/* Loading state */}
                    {productsLoading && (
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Text type="secondary">Đang tải danh sách sản phẩm...</Text>
                      </div>
                    )}

                    {/* Error state */}
                    {productsError && (
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Text type="danger">Lỗi tải dữ liệu: {productsError.message}</Text>
                      </div>
                    )}

                    {/* Show all products when no search */}
                    {!productSearch && !productsLoading && productVariants.length > 0 && (
                      <div style={{ padding: '12px 16px', backgroundColor: '#f6f8fa', borderBottom: '1px solid #f0f0f0' }}>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          Hiển thị {productVariants.length} sản phẩm gần đây
                        </Text>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Add product button */}
              {selectedProducts.length === 0 && (
                <div style={{
                  textAlign: 'center',
                  padding: '60px 20px',
                  backgroundColor: '#fafafa',
                  borderRadius: '8px',
                  border: '1px dashed #d9d9d9'
                }}>
                  <Empty
                    description={
                      <div>
                        <Text type="secondary" style={{ fontSize: 14 }}>
                          Chưa có thông tin sản phẩm
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          Tìm kiếm và thêm sản phẩm vào đơn hàng
                        </Text>
                      </div>
                    }
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  >
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        setProductDropdownOpen(true);
                        // Focus vào input search
                        setTimeout(() => {
                          const input = document.querySelector('input[placeholder*="Tìm theo tên, mã SKU"]');
                          if (input) input.focus();
                        }, 100);
                      }}
                      size="large"
                    >
                      Thêm sản phẩm
                    </Button>
                  </Empty>
                </div>
              )}

              {/* Selected products table */}
              {selectedProducts.length > 0 && (
                <div>
                  <div style={{
                    marginBottom: 16,
                    padding: '8px 12px',
                    backgroundColor: '#f6f8fa',
                    borderRadius: '6px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <Text style={{ fontSize: 14, fontWeight: 500 }}>
                      Đã chọn {selectedProducts.length} sản phẩm
                    </Text>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => setSelectedProducts([])}
                      style={{ color: '#ff4d4f' }}
                    >
                      Xóa tất cả
                    </Button>
                  </div>
                  <Table
                    columns={productColumns}
                    dataSource={selectedProducts}
                    rowKey="id"
                    pagination={false}
                    size="small"
                    scroll={{ x: 800 }}
                    bordered
                  />
                </div>
              )}
            </Card>

            {/* Delivery and Packaging Section */}
            <Card
              title="Đóng gói và giao hàng"
              style={{ marginBottom: 24 }}
            >
              <div style={{ marginBottom: 16 }}>
                <Text style={{ fontSize: 14, fontWeight: 500, marginBottom: 12, display: 'block' }}>
                  Chọn phương thức giao hàng:
                </Text>
                <div style={{ display: 'flex', gap: 12, flexWrap: 'wrap' }}>
                  <Button
                    type={deliveryMethod === DELIVERY_METHODS.STORE_PICKUP ? 'primary' : 'default'}
                    icon={<ShopOutlined />}
                    onClick={() => setDeliveryMethod(DELIVERY_METHODS.STORE_PICKUP)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      height: 'auto',
                      padding: '8px 16px'
                    }}
                  >
                    <div>
                      <div style={{ fontSize: 12, fontWeight: 500 }}>Đây qua hàng vận chuyển</div>
                    </div>
                  </Button>

                  <Button
                    type={deliveryMethod === DELIVERY_METHODS.DELIVERY ? 'primary' : 'default'}
                    icon={<TruckOutlined />}
                    onClick={() => setDeliveryMethod(DELIVERY_METHODS.DELIVERY)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      height: 'auto',
                      padding: '8px 16px'
                    }}
                  >
                    <div>
                      <div style={{ fontSize: 12, fontWeight: 500 }}>Đây vận chuyển ngoài</div>
                    </div>
                  </Button>

                  <Button
                    type={deliveryMethod === DELIVERY_METHODS.CUSTOMER_PICKUP ? 'primary' : 'default'}
                    icon={<HomeOutlined />}
                    onClick={() => setDeliveryMethod(DELIVERY_METHODS.CUSTOMER_PICKUP)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      height: 'auto',
                      padding: '8px 16px'
                    }}
                  >
                    <div>
                      <div style={{ fontSize: 12, fontWeight: 500 }}>Khách nhận tại cửa hàng</div>
                    </div>
                  </Button>

                  <Button
                    type={deliveryMethod === DELIVERY_METHODS.LATER_DELIVERY ? 'primary' : 'default'}
                    icon={<GiftOutlined />}
                    onClick={() => setDeliveryMethod(DELIVERY_METHODS.LATER_DELIVERY)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      height: 'auto',
                      padding: '8px 16px'
                    }}
                  >
                    <div>
                      <div style={{ fontSize: 12, fontWeight: 500 }}>Giao hàng sau</div>
                    </div>
                  </Button>
                </div>
              </div>

              <div style={{
                padding: '12px',
                backgroundColor: '#f6f8fa',
                borderRadius: '6px',
                fontSize: 12,
                color: '#666'
              }}>
                Bạn hãy thêm thông tin khách hàng để sử dụng dịch vụ giao hàng. Cập nhật{' '}
                <Button type="link" style={{ padding: 0, fontSize: 12, height: 'auto' }}>
                  tại đây
                </Button>
              </div>
            </Card>
          </Col>

          {/* Right Column */}
          <Col span={8}>
            {/* Order Summary */}
            <Card
              title="Thông tin bổ sung"
              style={{ marginBottom: 24 }}
            >
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong style={{ fontSize: 12 }}>Bán tại</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text style={{ fontSize: 12 }}>Chi nhánh mặc định</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <Text strong style={{ fontSize: 12 }}>Bán bởi</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text style={{ fontSize: 12 }}>Dương</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <Text strong style={{ fontSize: 12 }}>Nguồn</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text style={{ fontSize: 12 }}>Chọn nguồn</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <Text strong style={{ fontSize: 12 }}>Hẹn giao</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text style={{ fontSize: 12 }}>-</Text>
                  </div>
                </Col>
                <Col span={24}>
                  <Text strong style={{ fontSize: 12 }}>Mã đơn</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text style={{ fontSize: 12 }}>-</Text>
                  </div>
                </Col>
              </Row>

              <Divider style={{ margin: '16px 0' }} />

              <Form.Item name="ngay_dat_hang" label="Ngày đặt hàng">
                <DatePicker
                  style={{ width: '100%' }}
                  format="DD/MM/YYYY"
                />
              </Form.Item>

              <Form.Item name="trang_thai" label="Trạng thái">
                <Select>
                  <Option value={ORDER_STATUS.CHO_XU_LY}>{ORDER_STATUS_LABELS[ORDER_STATUS.CHO_XU_LY]}</Option>
                  <Option value={ORDER_STATUS.DA_XAC_NHAN}>{ORDER_STATUS_LABELS[ORDER_STATUS.DA_XAC_NHAN]}</Option>
                  <Option value={ORDER_STATUS.DA_DONG_GOI}>{ORDER_STATUS_LABELS[ORDER_STATUS.DA_DONG_GOI]}</Option>
                  <Option value={ORDER_STATUS.DA_GIAO}>{ORDER_STATUS_LABELS[ORDER_STATUS.DA_GIAO]}</Option>
                  <Option value={ORDER_STATUS.HOAN_THANH}>{ORDER_STATUS_LABELS[ORDER_STATUS.HOAN_THANH]}</Option>
                  <Option value={ORDER_STATUS.HUY}>{ORDER_STATUS_LABELS[ORDER_STATUS.HUY]}</Option>
                </Select>
              </Form.Item>

              <Form.Item name="ghi_chu" label="Ghi chú đơn hàng">
                <TextArea
                  rows={3}
                  placeholder="Ghi chú thêm về đơn hàng"
                />
              </Form.Item>
            </Card>

            {/* Pricing Summary */}
            <Card>
              <div style={{ marginBottom: 16 }}>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Checkbox
                    checked={isStockCheck}
                    onChange={(e) => setIsStockCheck(e.target.checked)}
                  >
                    Tích đúng tồn
                  </Checkbox>
                  <Button type="link" style={{ color: '#1890ff', fontSize: 12, padding: 0 }}>
                    Kiểm tra tồn kho
                  </Button>
                </Space>
              </div>

              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={12}>
                  <Select defaultValue="F10" style={{ width: '100%' }} size="small">
                    <Option value="F10">(F10)</Option>
                    <Option value="F11">(F11)</Option>
                  </Select>
                </Col>
                <Col span={12}>
                  <Select defaultValue="gia_ban_le" style={{ width: '100%' }} size="small">
                    <Option value="gia_ban_le">Giá bán lẻ</Option>
                    <Option value="gia_ban_buon">Giá bán buôn</Option>
                  </Select>
                </Col>
              </Row>

              <Divider style={{ margin: '16px 0' }} />

              {/* Order totals */}
              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Text style={{ fontSize: 14 }}>Tổng tiền ({selectedProducts.length} sản phẩm)</Text>
                  </Col>
                  <Col>
                    <Text style={{ fontSize: 14, fontWeight: 500 }}>
                      {subtotal.toLocaleString('vi-VN')}đ
                    </Text>
                  </Col>
                </Row>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Text style={{ fontSize: 14 }}>Chiết khấu (F6)</Text>
                  </Col>
                  <Col>
                    <Text style={{ fontSize: 14 }}>0đ</Text>
                  </Col>
                </Row>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Text style={{ fontSize: 14 }}>Phí giao hàng (F7)</Text>
                  </Col>
                  <Col>
                    <Text style={{ fontSize: 14 }}>0đ</Text>
                  </Col>
                </Row>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Text style={{ fontSize: 14 }}>Mã giảm giá</Text>
                  </Col>
                  <Col>
                    <Text style={{ fontSize: 14 }}>0đ</Text>
                  </Col>
                </Row>
              </div>

              <Divider style={{ margin: '16px 0' }} />

              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Space>
                      <Text style={{ fontSize: 16, fontWeight: 600 }}>Khách phải trả</Text>
                      <Switch
                        size="small"
                        checked={isManualTotalMode}
                        onChange={handleManualTotalToggle}
                        checkedChildren="Thủ công"
                        unCheckedChildren="Tự động"
                      />
                    </Space>
                  </Col>
                  <Col>
                    {isManualTotalMode ? (
                      <InputNumber
                        value={manualTotal}
                        onChange={handleManualTotalChange}
                        min={0}
                        formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value.replace(/\$\s?|(,*)/g, '')}
                        style={{
                          width: 150,
                          fontSize: 16,
                          fontWeight: 600,
                          color: '#1890ff'
                        }}
                        addonAfter="đ"
                      />
                    ) : (
                      <Text style={{ fontSize: 16, fontWeight: 600, color: '#1890ff' }}>
                        {total.toLocaleString('vi-VN')}đ
                      </Text>
                    )}
                  </Col>
                </Row>
              </div>

              {isManualTotalMode && (
                <div style={{ marginBottom: 12 }}>
                  <Text type="secondary" style={{ fontSize: 12, fontStyle: 'italic' }}>
                    💡 Chế độ thủ công: Bạn có thể điều chỉnh tổng tiền theo thực tế (giảm giá, khách đã trả một phần, v.v.)
                  </Text>
                </div>
              )}

              {/* Tiền cọc */}
              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Text style={{ fontSize: 14 }}>Tiền cọc</Text>
                  </Col>
                  <Col>
                    <InputNumber
                      value={depositAmount}
                      onChange={(value) => setDepositAmount(value || 0)}
                      min={0}
                      max={total}
                      formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value.replace(/\$\s?|(,*)/g, '')}
                      style={{ width: 120 }}
                      size="small"
                      addonAfter="đ"
                    />
                  </Col>
                </Row>
              </div>

              {/* Tiền COD */}
              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Text style={{ fontSize: 14 }}>Tiền COD</Text>
                    <div style={{ fontSize: 11, color: '#8c8c8c' }}>
                      CTV có thể bán giá cao hơn
                    </div>
                  </Col>
                  <Col>
                    <InputNumber
                      value={codAmount}
                      onChange={(value) => setCodAmount(value || 0)}
                      min={0}
                      formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value.replace(/\$\s?|(,*)/g, '')}
                      style={{ width: 120 }}
                      size="small"
                      addonAfter="đ"
                      placeholder="0"
                    />
                  </Col>
                </Row>
              </div>

              {/* Công nợ */}
              <div style={{
                marginBottom: 16,
                padding: '12px',
                backgroundColor: debtAmount !== 0 ? (debtAmount > 0 ? '#fff2f0' : '#f6ffed') : '#f5f5f5',
                borderRadius: '6px',
                border: `1px solid ${debtAmount > 0 ? '#ffccc7' : debtAmount < 0 ? '#b7eb8f' : '#d9d9d9'}`
              }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Text style={{
                      fontSize: 14,
                      fontWeight: 500,
                      color: debtAmount > 0 ? '#ff4d4f' : debtAmount < 0 ? '#52c41a' : '#666'
                    }}>
                      💰 {debtAmount === 0 ? 'Đã thanh toán đủ' : 'Công nợ'}
                    </Text>
                    {debtAmount !== 0 && (
                      <div style={{ fontSize: 12, color: '#8c8c8c', marginTop: 2 }}>
                        {debtAmount > 0 ? 'Khách còn nợ' : 'Mình nợ khách'}
                      </div>
                    )}
                  </Col>
                  <Col>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: 600,
                      color: debtAmount > 0 ? '#ff4d4f' : debtAmount < 0 ? '#52c41a' : '#666'
                    }}>
                      {debtAmount === 0 ? '0đ' : `${Math.abs(debtAmount).toLocaleString('vi-VN')}đ`}
                    </Text>
                  </Col>
                </Row>

                {/* Công thức tính */}
                <div style={{
                  marginTop: 8,
                  padding: '8px',
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  borderRadius: '4px',
                  fontSize: 12,
                  color: '#666'
                }}>
                  <Text style={{ fontSize: 12 }}>
                    💡 Công thức: Tiền sản phẩm ({total.toLocaleString('vi-VN')}đ) - Tiền cọc ({depositAmount.toLocaleString('vi-VN')}đ) - Tiền COD ({codAmount.toLocaleString('vi-VN')}đ) = {debtAmount.toLocaleString('vi-VN')}đ
                  </Text>
                </div>
              </div>

              {/* Customer debt info */}
              <div style={{
                padding: '12px',
                backgroundColor: '#f6f8fa',
                borderRadius: '6px',
                marginBottom: 16
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                  <Text style={{ fontSize: 12, color: '#1890ff' }}>Khách đã trả</Text>
                  <Button type="link" style={{ fontSize: 12, padding: 0, height: 'auto' }}>
                    Thêm phương thức
                  </Button>
                </div>
                <Text style={{ fontSize: 11, color: '#666' }}>
                  VD: Hàng tăng gói riêng
                </Text>
              </div>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default CreateOrder;
