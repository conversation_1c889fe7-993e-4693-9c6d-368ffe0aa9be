import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Row,
  Col,
  Statistic,
  DatePicker,
  Modal,
  Form,
  InputNumber,
  message,
  Tabs,
  Spin,
} from "antd";
import {
  SearchOutlined,
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  HistoryOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import {
  useStockMovements,
  useCreateStockMovement,
  useWarehouses,
  useProductVariantsForMovements,
} from "../../hooks/useWarehouses";

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const StockMovements = () => {
  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [selectedWarehouse, setSelectedWarehouse] = useState("all");
  const [movementType, setMovementType] = useState("all");
  const [dateRange, setDateRange] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState("nhap"); // 'nhap' or 'xuat'
  const [form] = Form.useForm();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
      setCurrentPage(1); // Reset to first page when searching
    }, 500);

    return () => clearTimeout(timer);
  }, [searchText]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedWarehouse, movementType, dateRange]);

  // API hooks
  const { data: warehouses = [], isLoading: warehousesLoading } =
    useWarehouses();
  const { data: productVariants = [], isLoading: productsLoading } =
    useProductVariantsForMovements();
  console.log("data", productVariants);

  // Build query params for stock movements
  const movementParams = {
    page: currentPage,
    limit: pageSize,
    warehouse_id: selectedWarehouse !== "all" ? selectedWarehouse : undefined,
    type: movementType !== "all" ? movementType : undefined,
    start_date:
      dateRange.length === 2 ? dateRange[0].format("YYYY-MM-DD") : undefined,
    end_date:
      dateRange.length === 2 ? dateRange[1].format("YYYY-MM-DD") : undefined,
  };

  const {
    data: movementsResponse = { data: [], pagination: {} },
    isLoading: movementsLoading,
    refetch: refetchMovements,
  } = useStockMovements(movementParams);

  const createStockMovementMutation = useCreateStockMovement();

  // Extract data from response
  const movements = movementsResponse.data || [];
  const pagination = movementsResponse.pagination || {};

  // Check if any data is loading
  const isLoading = movementsLoading || warehousesLoading || productsLoading;

  const handleAddMovement = (type) => {
    setModalType(type);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleSubmitMovement = async (values) => {
    try {
      // Validate stock for export
      if (modalType === "xuat") {
        const selectedVariant = productVariants.find(
          (v) => v.id === values.phien_ban_id
        );
        if (selectedVariant && values.so_luong > selectedVariant.so_luong_ton) {
          message.error(
            `Số lượng xuất (${values.so_luong}) vượt quá tồn kho (${selectedVariant.so_luong_ton})`
          );
          return;
        }
      }

      const movementData = {
        san_pham_id: values.phien_ban_id, // Gửi phien_ban_id như san_pham_id
        kho_id: values.kho_id,
        so_luong: values.so_luong,
        gia_von: values.gia_von || 0,
        ly_do: values.ly_do,
        ghi_chu: values.ghi_chu || "",
        loai_giao_dich: modalType,
      };

      await createStockMovementMutation.mutateAsync(movementData);

      setIsModalVisible(false);
      form.resetFields();
      refetchMovements();
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const getMovementTypeTag = (type) => {
    const types = {
      nhap: { color: "green", text: "Nhập kho", icon: <ArrowDownOutlined /> },
      xuat: { color: "red", text: "Xuất kho", icon: <ArrowUpOutlined /> },
      chuyen: { color: "blue", text: "Chuyển kho", icon: <ExportOutlined /> },
      dieu_chinh: {
        color: "orange",
        text: "Điều chỉnh",
        icon: <HistoryOutlined />,
      },
    };
    const typeInfo = types[type] || { color: "default", text: type };
    return (
      <Tag color={typeInfo.color} icon={typeInfo.icon}>
        {typeInfo.text}
      </Tag>
    );
  };

  const columns = [
    {
      title: "Thời gian",
      dataIndex: "ngay_thuc_hien",
      key: "ngay_thuc_hien",
      width: 150,
      render: (date) => (
        <div>
          <div>{dayjs(date).format("DD/MM/YYYY")}</div>
          <Text type="secondary" style={{ fontSize: "12px" }}>
            {dayjs(date).format("HH:mm")}
          </Text>
        </div>
      ),
    },
    {
      title: "Loại",
      dataIndex: "loai",
      key: "loai",
      width: 120,
      render: (type) => getMovementTypeTag(type),
    },
    {
      title: "Sản phẩm",
      key: "product",
      width: 250,
      render: (_, record) => (
        <div>
          <Text strong>{record.san_pham}</Text>
          <br />
          <Tag size="small">{record.ma_sku}</Tag>
        </div>
      ),
    },
    {
      title: "Kho",
      dataIndex: "ten_kho",
      key: "ten_kho",
      width: 150,
      render: (text, record) => (
        <div>
          <div>{text}</div>
          {record.kho_dich && (
            <Text type="secondary" style={{ fontSize: "12px" }}>
              → {record.kho_dich}
            </Text>
          )}
        </div>
      ),
    },
    {
      title: "Số lượng",
      dataIndex: "so_luong",
      key: "so_luong",
      width: 100,
      align: "center",
      render: (value, record) => (
        <Text
          strong
          style={{
            color: record.loai === "nhap" || value > 0 ? "#52c41a" : "#ff4d4f",
          }}
        >
          {value > 0 ? "+" : ""}
          {value}
        </Text>
      ),
    },
    {
      title: "Giá trị",
      dataIndex: "tong_gia_tri",
      key: "tong_gia_tri",
      width: 120,
      align: "right",
      render: (value) => (
        <Text style={{ color: value >= 0 ? "#52c41a" : "#ff4d4f" }}>
          {value?.toLocaleString("vi-VN")} đ
        </Text>
      ),
    },
    {
      title: "Lý do",
      dataIndex: "ly_do",
      key: "ly_do",
      ellipsis: true,
    },
    {
      title: "Người thực hiện",
      dataIndex: "nguoi_thuc_hien",
      key: "nguoi_thuc_hien",
      width: 120,
    },
  ];

  // Calculate statistics from current data
  const safeMovements = Array.isArray(movements) ? movements : [];
  const totalIn = safeMovements
    .filter((m) => m.loai === "nhap")
    .reduce((sum, m) => sum + (m.so_luong || 0), 0);
  const totalOut = safeMovements
    .filter((m) => m.loai === "xuat")
    .reduce((sum, m) => sum + Math.abs(m.so_luong || 0), 0);
  const totalValue = safeMovements.reduce(
    (sum, m) => sum + (m.tong_gia_tri || 0),
    0
  );
  const todayMovements = safeMovements.filter((m) =>
    dayjs(m.ngay_thuc_hien).isSame(dayjs(), "day")
  ).length;

  // Show loading spinner
  if (isLoading && (!movements || movements.length === 0)) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "400px",
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng nhập (tháng)"
              value={totalIn}
              prefix={<ArrowDownOutlined />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng xuất (tháng)"
              value={totalOut}
              prefix={<ArrowUpOutlined />}
              valueStyle={{ color: "#ff4d4f" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Giá trị ròng"
              value={totalValue}
              prefix="₫"
              valueStyle={{ color: totalValue >= 0 ? "#52c41a" : "#ff4d4f" }}
              formatter={(value) => value?.toLocaleString("vi-VN")}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Giao dịch hôm nay"
              value={todayMovements}
              prefix={<HistoryOutlined />}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col>
              <Title level={4} style={{ margin: 0 }}>
                Lịch sử xuất nhập kho
              </Title>
            </Col>
            <Col flex="auto">
              <Space style={{ float: "right" }}>
                <Input
                  placeholder="Tìm kiếm..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 200 }}
                  allowClear
                />
                <Select
                  value={selectedWarehouse}
                  onChange={setSelectedWarehouse}
                  style={{ width: 150 }}
                >
                  <Option value="all">Tất cả kho</Option>
                  {warehouses.map((warehouse) => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.ten_kho}
                    </Option>
                  ))}
                </Select>
                <Select
                  value={movementType}
                  onChange={setMovementType}
                  style={{ width: 120 }}
                >
                  <Option value="all">Tất cả</Option>
                  <Option value="nhap">Nhập kho</Option>
                  <Option value="xuat">Xuất kho</Option>
                  <Option value="chuyen">Chuyển kho</Option>
                  <Option value="dieu_chinh">Điều chỉnh</Option>
                </Select>
                <RangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  format="DD/MM/YYYY"
                  placeholder={["Từ ngày", "Đến ngày"]}
                />
                <Button icon={<SyncOutlined />} onClick={refetchMovements}>
                  Làm mới
                </Button>
                <Button
                  type="primary"
                  icon={<ImportOutlined />}
                  onClick={() => handleAddMovement("nhap")}
                >
                  Nhập kho
                </Button>
                <Button
                  icon={<ExportOutlined />}
                  onClick={() => handleAddMovement("xuat")}
                >
                  Xuất kho
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={movements}
          loading={isLoading || createStockMovementMutation.isLoading}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: pagination.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} giao dịch`,
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
                setCurrentPage(1); // Reset to first page when changing page size
              }
            },
            onShowSizeChange: (current, size) => {
              setPageSize(size);
              setCurrentPage(1); // Reset to first page when changing page size
            },
            pageSizeOptions: ["10", "20", "50", "100"],
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* Add Movement Modal */}
      <Modal
        title={modalType === "nhap" ? "Nhập kho" : "Xuất kho"}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmitMovement}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phien_ban_id"
                label="Sản phẩm"
                rules={[{ required: true, message: "Vui lòng chọn sản phẩm" }]}
              >
                <Select
                  placeholder="Chọn sản phẩm"
                  showSearch
                  loading={productsLoading}
                  filterOption={(input, option) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {Array.isArray(productVariants) &&
                    productVariants.map((variant) => (
                      <Option key={variant.id} value={variant.id}>
                        {variant.ten} ({variant.ma || "N/A"}) - Tồn:{" "}
                        {variant.so_luong_ton || 0}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="kho_id"
                label="Kho hàng"
                rules={[{ required: true, message: "Vui lòng chọn kho" }]}
              >
                <Select placeholder="Chọn kho" loading={warehousesLoading}>
                  {Array.isArray(warehouses) &&
                    warehouses.map((warehouse) => (
                      <Option key={warehouse.id} value={warehouse.id}>
                        {warehouse.ten_kho}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="so_luong"
                label="Số lượng"
                rules={[
                  { required: true, message: "Vui lòng nhập số lượng" },
                  {
                    type: "number",
                    min: 1,
                    message: "Số lượng phải lớn hơn 0",
                  },
                ]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  min={1}
                  placeholder="Nhập số lượng"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="gia_von"
                label="Giá vốnn"
                rules={[
                  {
                    type: "number",
                    min: 0,
                    message: "Giá vốn phải lớn hơn hoặc bằng 0",
                  },
                ]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  min={0}
                  placeholder="Nhập giá vốn"
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }
                  parser={(value) => value.replace(/\$\s?|(,*)/g, "")}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="ly_do"
            label="Lý do"
            rules={[{ required: true, message: "Vui lòng nhập lý do" }]}
          >
            <Input placeholder="Nhập lý do xuất/nhập kho" />
          </Form.Item>

          <Form.Item name="ghi_chu" label="Ghi chú">
            <Input.TextArea rows={3} placeholder="Nhập ghi chú (tùy chọn)" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>Hủy</Button>
              <Button type="primary" htmlType="submit">
                {modalType === "nhap" ? "Nhập kho" : "Xuất kho"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default StockMovements;
