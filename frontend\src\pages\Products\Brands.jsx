import React, { useState } from 'react';
import { Tag, Modal, Form, Input, Image } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

import { DataTable, SearchFilter, PageHeader } from '../../components/Common';
import { usePermissions } from '../../contexts/PermissionContext';
import { useBrands, useCreateBrand, useUpdateBrand, useDeleteBrand } from '../../hooks/useProducts';
import ImageUpload from '../../components/ImageUpload/ImageUpload';

const Brands = () => {
  const { canCreate, canEdit, canDelete } = usePermissions();

  // State
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingBrand, setEditingBrand] = useState(null);
  const [form] = Form.useForm();

  // API hooks
  const { data: brands = [], isLoading: loading, error } = useBrands();
  const createBrandMutation = useCreateBrand();
  const updateBrandMutation = useUpdateBrand();
  const deleteBrandMutation = useDeleteBrand();



  // Table columns
  const columns = [
    {
      title: 'Logo',
      dataIndex: 'logo',
      key: 'logo',
      width: 80,
      render: (url, record) => {
        if (!url) {
          return (
            <div
              style={{
                width: 50,
                height: 50,
                backgroundColor: '#f5f5f5',
                borderRadius: 4,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999',
                fontSize: 12
              }}
            >
              No Logo
            </div>
          );
        }

        return (
          <Image
            src={url}
            alt={record.ten}
            width={50}
            height={50}
            style={{ objectFit: 'contain', borderRadius: 4 }}
          />
        );
      }
    },
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (text) => <Tag color="blue">#{text}</Tag>
    },
    {
      title: 'Tên nhãn hiệu',
      dataIndex: 'ten',
      key: 'ten',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          {record.website && (
            <a
              href={record.website}
              target="_blank"
              rel="noopener noreferrer"
              style={{ fontSize: 12, color: '#1890ff' }}
            >
              {record.website}
            </a>
          )}
        </div>
      )
    },
    {
      title: 'Mô tả',
      dataIndex: 'mo_ta',
      key: 'mo_ta',
      render: (text) => text || <span style={{ color: '#999', fontStyle: 'italic' }}>Chưa có mô tả</span>
    },
    {
      title: 'Người tạo',
      dataIndex: 'nguoi_tao',
      key: 'nguoi_tao',
      width: 120
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'ngay_tao',
      key: 'ngay_tao',
      width: 150,
      render: (date) => new Date(date).toLocaleDateString('vi-VN')
    }
  ];

  // Event handlers
  const handleCreate = () => {
    setEditingBrand(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingBrand(record);

    // Xử lý logo để hiển thị trong form
    const formValues = {
      ...record,
      logo: record.logo ? [{
        uid: '1',
        name: 'logo',
        status: 'done',
        url: record.logo
      }] : []
    };

    form.setFieldsValue(formValues);
    setIsModalVisible(true);
  };

  const handleDelete = async (record) => {
    try {
      await deleteBrandMutation.mutateAsync(record.id);
    } catch (error) {
      // Error message handled by mutation
    }
  };

  const handleSubmit = async (values) => {
    try {
      // Xử lý logo - lấy URL từ file upload
      const processedValues = {
        ...values,
        logo: values.logo && values.logo.length > 0 ? values.logo[0].url : null
      };

      if (editingBrand) {
        await updateBrandMutation.mutateAsync({
          id: editingBrand.id,
          data: processedValues
        });
      } else {
        await createBrandMutation.mutateAsync(processedValues);
      }

      setIsModalVisible(false);
      form.resetFields();
      setEditingBrand(null);
    } catch (error) {
      // Error message handled by mutation
    }
  };



  const actions = [];
  if (canCreate('NHAN_HIEU')) {
    actions.push({
      type: 'primary',
      icon: <PlusOutlined />,
      label: 'Thêm nhãn hiệu',
      onClick: handleCreate
    });
  }

  return (
    <div>
      <PageHeader
        title="Nhãn hiệu"
        subTitle="Quản lý thương hiệu sản phẩm"
        actions={actions}
      />

      <DataTable
        data={brands}
        columns={columns}
        loading={loading}
        onEdit={canEdit('NHAN_HIEU') ? handleEdit : undefined}
        onDelete={canDelete('NHAN_HIEU') ? handleDelete : undefined}
        rowKey="id"
      />

      <Modal
        title={editingBrand ? 'Chỉnh sửa nhãn hiệu' : 'Thêm nhãn hiệu'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
        confirmLoading={createBrandMutation.isLoading || updateBrandMutation.isLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="ten"
            label="Tên nhãn hiệu"
            rules={[{ required: true, message: 'Vui lòng nhập tên nhãn hiệu' }]}
          >
            <Input placeholder="Nhập tên nhãn hiệu" />
          </Form.Item>

          <Form.Item
            name="mo_ta"
            label="Mô tả"
          >
            <Input.TextArea
              placeholder="Nhập mô tả nhãn hiệu"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="website"
            label="Website"
          >
            <Input placeholder="https://example.com" />
          </Form.Item>

          <Form.Item
            name="logo"
            label="Logo"
          >
            <ImageUpload
              maxCount={1}
              folder="brands"
              width={200}
              height={200}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Brands;
