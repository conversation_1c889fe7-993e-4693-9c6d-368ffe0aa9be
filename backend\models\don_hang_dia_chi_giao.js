'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DonHangDiaChiGiao extends Model {
    static associate(models) {
      // Quan hệ với đơn hàng
      DonHangDiaChiGiao.belongsTo(models.DonHang, {
        foreignKey: 'don_hang_id',
        as: 'donHang'
      });
    }
  }

  DonHangDiaChiGiao.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    don_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'don_hang',
        key: 'id'
      }
    },
    dia_chi: {
      type: DataTypes.STRING,
      allowNull: false
    },
    phuong_xa: {
      type: DataTypes.STRING,
      allowNull: true
    },
    quan_huyen: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tinh_thanh: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_nhan: {
      type: DataTypes.STRING,
      allowNull: true
    },
    so_dien_thoai_nhan: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'DonHangDiaChiGiao',
    tableName: 'don_hang_dia_chi_giao',
    timestamps: false
  });

  return DonHangDiaChiGiao;
};
