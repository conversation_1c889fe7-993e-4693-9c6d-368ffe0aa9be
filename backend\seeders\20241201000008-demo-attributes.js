'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Insert attributes
    await queryInterface.bulkInsert('thuoc_tinh', [
      {
        ten: '<PERSON><PERSON><PERSON> sắc',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Kích thước',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Chất liệu',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Phong cách',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});

    // Insert attribute values
    await queryInterface.bulkInsert('gia_tri_thuoc_tinh', [
      // <PERSON><PERSON><PERSON> s<PERSON>c
      { thuoc_tinh_id: 1, gia_tri: 'Đỏ', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 1, gia_tri: 'Xanh', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 1, gia_tri: 'Vàng', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 1, gia_tri: 'Đen', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 1, gia_tri: 'Trắng', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 1, gia_tri: 'Xám', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },

      // Kích thước
      { thuoc_tinh_id: 2, gia_tri: 'XS', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 2, gia_tri: 'S', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 2, gia_tri: 'M', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 2, gia_tri: 'L', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 2, gia_tri: 'XL', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 2, gia_tri: 'XXL', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },

      // Chất liệu
      { thuoc_tinh_id: 3, gia_tri: 'Cotton', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 3, gia_tri: 'Polyester', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 3, gia_tri: 'Jean', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 3, gia_tri: 'Da', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 3, gia_tri: 'Vải thun', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },

      // Phong cách
      { thuoc_tinh_id: 4, gia_tri: 'Thể thao', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 4, gia_tri: 'Công sở', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 4, gia_tri: 'Thời trang', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 4, gia_tri: 'Casual', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() },
      { thuoc_tinh_id: 4, gia_tri: 'Vintage', nguoi_tao: 'admin', ngay_tao: new Date(), ngay_cap_nhap: new Date() }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('gia_tri_thuoc_tinh', null, {});
    await queryInterface.bulkDelete('thuoc_tinh', null, {});
  }
};
