{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "react-query": "^3.39.3", "dayjs": "^1.11.10", "recharts": "^2.8.0", "react-hook-form": "^7.48.2", "js-cookie": "^3.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/js-cookie": "^3.0.6", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}