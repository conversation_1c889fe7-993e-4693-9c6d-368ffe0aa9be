import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin, Result, Button } from 'antd';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../contexts/PermissionContext';

const ProtectedRoute = ({ 
  children, 
  requiredPermission = null, 
  requiredRole = null,
  requireAdmin = false 
}) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { hasPermission, hasRole, isAdmin } = usePermissions();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" tip="Đang tải..." />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check admin requirement
  if (requireAdmin && !isAdmin()) {
    return (
      <Result
        status="403"
        title="403"
        subTitle="Bạn cần quyền quản trị viên để truy cập trang này."
        extra={
          <Button type="primary" onClick={() => window.history.back()}>
            Quay lại
          </Button>
        }
      />
    );
  }

  // Check role requirement
  if (requiredRole && !hasRole(requiredRole) && !isAdmin()) {
    return (
      <Result
        status="403"
        title="403"
        subTitle={`Bạn cần vai trò "${requiredRole}" để truy cập trang này.`}
        extra={
          <Button type="primary" onClick={() => window.history.back()}>
            Quay lại
          </Button>
        }
      />
    );
  }

  // Check permission requirement
  if (requiredPermission && !hasPermission(requiredPermission) && !isAdmin()) {
    return (
      <Result
        status="403"
        title="403"
        subTitle="Bạn không có quyền truy cập trang này."
        extra={
          <Button type="primary" onClick={() => window.history.back()}>
            Quay lại
          </Button>
        }
      />
    );
  }

  // Check if user account is active
  if (user?.trang_thai !== 'dang_giao_dich') {
    return (
      <Result
        status="warning"
        title="Tài khoản bị khóa"
        subTitle="Tài khoản của bạn đã bị khóa. Vui lòng liên hệ quản trị viên."
        extra={
          <Button type="primary" onClick={() => window.location.href = '/login'}>
            Đăng nhập lại
          </Button>
        }
      />
    );
  }

  // All checks passed, render children
  return children;
};

export default ProtectedRoute;
