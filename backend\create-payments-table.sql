USE sale_system;

CREATE TABLE IF NOT EXISTS `payments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `customer_id` int NOT NULL,
  `type` enum('thu','chi') NOT NULL COMMENT 'thu = thu tiền (gi<PERSON><PERSON> công nợ), chi = chi tiền (tăng công nợ)',
  `amount` double NOT NULL,
  `payment_method` enum('cash','transfer','card','other') NOT NULL DEFAULT 'cash',
  `note` text,
  `is_partial_payment` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'true = thanh toán một phần, false = thanh toán toàn bộ',
  `created_by` varchar(100) NOT NULL,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON><PERSON>AR<PERSON>EY (`id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `nguoi_dung` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
