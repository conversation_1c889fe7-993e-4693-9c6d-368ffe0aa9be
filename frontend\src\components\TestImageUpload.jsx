import React, { useState } from 'react';
import { Card, Space, Alert } from 'antd';
import ImageUpload from './ImageUpload/ImageUpload';

const TestImageUpload = () => {
  const [images, setImages] = useState([]);

  const handleImagesChange = (newImages) => {
    console.log('🎯 TestImageUpload - handleImagesChange called:', newImages);
    setImages(newImages);
  };

  return (
    <Card title="🧪 Test ImageUpload Component" style={{ maxWidth: 800, margin: '20px auto' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Alert
          message="Test ImageUpload Component"
          description="This is a simple test to check if ImageUpload component works correctly. Check browser console for detailed logs."
          type="info"
          showIcon
        />

        <div>
          <h4>📤 Upload Images:</h4>
          <ImageUpload
            value={images}
            onChange={handleImagesChange}
            maxCount={5}
            folder="test-upload"
            width={600}
            height={400}
          />
        </div>

        <div>
          <h4>📋 Current Images State:</h4>
          {images.length === 0 ? (
            <p style={{ color: '#999' }}>No images uploaded yet</p>
          ) : (
            <div style={{ background: '#f5f5f5', padding: 16, borderRadius: 8 }}>
              <p><strong>Count:</strong> {images.length}</p>
              {images.map((img, index) => (
                <div key={img.uid || index} style={{ marginBottom: 8, fontSize: 12 }}>
                  <div><strong>Image {index + 1}:</strong></div>
                  <div>• Name: {img.name}</div>
                  <div>• URL: {img.url}</div>
                  <div>• Public ID: {img.public_id}</div>
                  <div>• Status: {img.status}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        <Alert
          message="Debug Steps"
          description={
            <div>
              <p><strong>What to check:</strong></p>
              <ol>
                <li>Open browser console (F12)</li>
                <li>Select an image file</li>
                <li>Watch for console logs starting with 📁, 🎯, 📤, etc.</li>
                <li>Check if image appears in the upload area</li>
                <li>Check if "Current Images State" updates</li>
              </ol>
              <p><strong>Expected behavior:</strong></p>
              <ul>
                <li>File selection should trigger console logs</li>
                <li>Upload should start automatically</li>
                <li>Image should appear in upload area when done</li>
                <li>State should update with image data</li>
              </ul>
            </div>
          }
          type="warning"
        />
      </Space>
    </Card>
  );
};

export default TestImageUpload;
