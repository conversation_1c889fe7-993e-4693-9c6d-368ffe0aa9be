'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DiaChiNguoiDung extends Model {
    static associate(models) {
      // Quan hệ với người dùng
      DiaChiNguoiDung.belongsTo(models.NguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'nguoiDung'
      });
    }
  }

  DiaChiNguoiDung.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nguoi_dung_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    dia_chi: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    phuong_xa: {
      type: DataTypes.STRING,
      allowNull: true
    },
    quan_huyen: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tinh_thanh: {
      type: DataTypes.STRING,
      allowNull: true
    },
    mac_dinh: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    sequelize,
    modelName: 'Dia<PERSON>hiNguoiDung',
    tableName: 'dia_chi_nguoi_dung',
    timestamps: false
  });

  return DiaChiNguoiDung;
};
