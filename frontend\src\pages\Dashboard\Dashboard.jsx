import React from "react";
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Space,
  Table,
  Tag,
  Progress,
  List,
  Avatar,
  Spin,
  Alert,
  Button,
} from "antd";
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  ShoppingOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from "recharts";

import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../contexts/PermissionContext";
import { useDashboardData } from "../../hooks/useDashboard";

const { Title, Text } = Typography;

const Dashboard = () => {
  const { user } = useAuth();
  const { canView } = usePermissions();

  // <PERSON><PERSON>y d<PERSON> li<PERSON>u thật từ API
  const {
    stats,
    salesChart,
    recentOrders,
    topProducts,
    isLoading,
    hasError,
    refetchAll,
  } = useDashboardData();

  // Dữ liệu thật từ API
  const salesData = salesChart.data || [];
  const ordersData = recentOrders.data || [];
  const productsData = topProducts.data || [];
  const statsData = stats.data || {};

  // Tạo dữ liệu category từ top products
  const categoryData = productsData.slice(0, 4).map((product, index) => ({
    name: product.title || `Sản phẩm ${index + 1}`,
    value: product.sales || 0,
    color: ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"][index] || "#8884d8",
  }));

  const orderColumns = [
    {
      title: "Mã đơn hàng",
      dataIndex: "ma_don_hang",
      key: "ma_don_hang",
    },
    {
      title: "Khách hàng",
      dataIndex: "khach_hang",
      key: "khach_hang",
    },
    {
      title: "Tổng tiền",
      dataIndex: "tong_tien",
      key: "tong_tien",
      render: (value) => `${value.toLocaleString()} đ`,
    },
    {
      title: "Trạng thái",
      dataIndex: "trang_thai",
      key: "trang_thai",
      render: (status) => {
        const statusMap = {
          da_dong_goi: { color: "orange", text: "Hoàn thành" },
          hoan_thanh: { color: "orange", text: "Hoàn thành" },
          hoan_hang: { color: "orange", text: "Hoàn hàng" },
          cho_xu_ly: { color: "orange", text: "Chờ xử lý" },
          da_xac_nhan: { color: "blue", text: "Đã xác nhận" },
          da_giao: { color: "green", text: "Đã giao" },
        };
        const { color, text } = statusMap[status] || {
          color: "default",
          text: status,
        };
        return <Tag color={color}>{text}</Tag>;
      },
    },
  ];

  // Loading state
  if (isLoading) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
        <div style={{ marginTop: "20px" }}>Đang tải dữ liệu dashboard...</div>
      </div>
    );
  }

  // Error state
  if (hasError) {
    return (
      <Alert
        message="Lỗi tải dữ liệu"
        description="Không thể tải dữ liệu dashboard. Vui lòng thử lại sau."
        type="error"
        showIcon
        action={
          <Button type="primary" onClick={refetchAll}>
            Thử lại
          </Button>
        }
        style={{ margin: "20px" }}
      />
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Tổng quan</Title>
        <Text type="secondary">
          Chào mừng {user?.ho_ten} quay trở lại! Đây là tổng quan về hoạt động
          kinh doanh.
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Doanh thu hôm nay"
              value={statsData.today_revenue || 0}
              precision={0}
              valueStyle={{
                color: statsData.revenue_change >= 0 ? "#3f8600" : "#cf1322",
              }}
              prefix={<DollarOutlined />}
              suffix="đ"
            />
            <div style={{ marginTop: 8 }}>
              {statsData.revenue_change >= 0 ? (
                <ArrowUpOutlined style={{ color: "#3f8600" }} />
              ) : (
                <ArrowDownOutlined style={{ color: "#cf1322" }} />
              )}{" "}
              {Math.abs(statsData.revenue_change || 0).toFixed(1)}%
              <Text type="secondary" style={{ marginLeft: 8 }}>
                so với hôm qua
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Đơn hàng mới"
              value={statsData.today_orders || 0}
              valueStyle={{ color: "#1890ff" }}
              prefix={<ShoppingCartOutlined />}
            />
            <div style={{ marginTop: 8 }}>
              {statsData.orders_change >= 0 ? (
                <ArrowUpOutlined style={{ color: "#3f8600" }} />
              ) : (
                <ArrowDownOutlined style={{ color: "#cf1322" }} />
              )}{" "}
              {Math.abs(statsData.orders_change || 0).toFixed(1)}%
              <Text type="secondary" style={{ marginLeft: 8 }}>
                so với hôm qua
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Khách hàng mới"
              value={statsData.today_customers || 0}
              valueStyle={{ color: "#722ed1" }}
              prefix={<UserOutlined />}
            />
            <div style={{ marginTop: 8 }}>
              {statsData.customers_change >= 0 ? (
                <ArrowUpOutlined style={{ color: "#3f8600" }} />
              ) : (
                <ArrowDownOutlined style={{ color: "#cf1322" }} />
              )}{" "}
              {Math.abs(statsData.customers_change || 0).toFixed(1)}%
              <Text type="secondary" style={{ marginLeft: 8 }}>
                so với hôm qua
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Sản phẩm bán chạy"
              value={statsData.week_products_sold || 0}
              valueStyle={{ color: "#fa8c16" }}
              prefix={<ShoppingOutlined />}
            />
            <div style={{ marginTop: 8 }}>
              {statsData.products_change >= 0 ? (
                <ArrowUpOutlined style={{ color: "#3f8600" }} />
              ) : (
                <ArrowDownOutlined style={{ color: "#cf1322" }} />
              )}{" "}
              {Math.abs(statsData.products_change || 0).toFixed(1)}%
              <Text type="secondary" style={{ marginLeft: 8 }}>
                so với tuần trước
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts Row */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {/* Sales Chart */}
        <Col xs={24} lg={16}>
          <Card title="Doanh thu 7 ngày qua" style={{ height: 400 }}>
            {salesChart.isLoading ? (
              <div style={{ textAlign: "center", padding: "50px" }}>
                <Spin size="large" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={salesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [
                      `${value.toLocaleString()} đ`,
                      "Doanh thu",
                    ]}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#1890ff"
                    strokeWidth={2}
                    dot={{ fill: "#1890ff" }}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </Card>
        </Col>

        {/* Category Chart */}
        <Col xs={24} lg={8}>
          <Card title="Top sản phẩm bán chạy" style={{ height: 400 }}>
            {topProducts.isLoading ? (
              <div style={{ textAlign: "center", padding: "50px" }}>
                <Spin size="large" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            )}
          </Card>
        </Col>
      </Row>

      {/* Tables Row */}
      <Row gutter={[16, 16]}>
        {/* Recent Orders */}
        {canView("DON_HANG") && (
          <Col xs={24} lg={14}>
            <Card
              title="Đơn hàng gần đây"
              extra={<a href="/orders">Xem tất cả</a>}
            >
              <Table
                dataSource={ordersData}
                columns={orderColumns}
                pagination={false}
                size="small"
                loading={recentOrders.isLoading}
              />
            </Card>
          </Col>
        )}

        {/* Top Products */}
        {canView("SAN_PHAM") && (
          <Col xs={24} lg={10}>
            <Card
              title="Sản phẩm bán chạy"
              extra={<a href="/products">Xem tất cả</a>}
            >
              <List
                itemLayout="horizontal"
                dataSource={productsData}
                loading={topProducts.isLoading}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar>{item.avatar}</Avatar>}
                      title={item.title}
                      description={item.description}
                    />
                    <div>
                      <Text strong>{item.sales} đã bán</Text>
                      <Progress
                        percent={(item.sales / 50) * 100}
                        size="small"
                        showInfo={false}
                        style={{ width: 60, marginTop: 4 }}
                      />
                    </div>
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        )}
      </Row>
    </div>
  );
};

export default Dashboard;
