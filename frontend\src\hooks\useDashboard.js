import { useQuery } from 'react-query';
import { testAPI } from '../services/api';

// Hook để lấy thống kê tổng quan dashboard
export const useDashboardStats = () => {
  return useQuery(
    ['dashboard-stats'],
    () => testAPI.getDashboardStats(),
    {
      refetchInterval: 5 * 60 * 1000, // Refresh mỗi 5 phút
      refetchOnWindowFocus: true,
      staleTime: 2 * 60 * 1000, // Dữ liệu cũ sau 2 phút
      cacheTime: 10 * 60 * 1000, // Cache 10 phút
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
    }
  );
};

// Hook để lấy dữ liệu biểu đồ doanh thu
export const useSalesChart = () => {
  return useQuery(
    ['sales-chart'],
    () => testAPI.getSalesChart(),
    {
      refetchInterval: 10 * 60 * 1000, // Refresh mỗi 10 phút
      refetchOnWindowFocus: true,
      staleTime: 5 * 60 * 1000, // Dữ liệu cũ sau 5 phút
      cacheTime: 15 * 60 * 1000, // Cache 15 phút
      retry: 3
    }
  );
};

// Hook để lấy đơn hàng gần đây
export const useRecentOrders = () => {
  return useQuery(
    ['recent-orders'],
    () => testAPI.getRecentOrders(),
    {
      refetchInterval: 2 * 60 * 1000, // Refresh mỗi 2 phút
      refetchOnWindowFocus: true,
      staleTime: 1 * 60 * 1000, // Dữ liệu cũ sau 1 phút
      cacheTime: 5 * 60 * 1000, // Cache 5 phút
      retry: 3
    }
  );
};

// Hook để lấy sản phẩm bán chạy
export const useTopProducts = () => {
  return useQuery(
    ['top-products'],
    () => testAPI.getTopProducts(),
    {
      refetchInterval: 30 * 60 * 1000, // Refresh mỗi 30 phút
      refetchOnWindowFocus: false,
      staleTime: 15 * 60 * 1000, // Dữ liệu cũ sau 15 phút
      cacheTime: 60 * 60 * 1000, // Cache 1 giờ
      retry: 3
    }
  );
};

// Hook tổng hợp tất cả dữ liệu dashboard
export const useDashboardData = () => {
  const statsQuery = useDashboardStats();
  const salesChartQuery = useSalesChart();
  const recentOrdersQuery = useRecentOrders();
  const topProductsQuery = useTopProducts();

  return {
    stats: {
      data: statsQuery.data?.data,
      isLoading: statsQuery.isLoading,
      error: statsQuery.error,
      refetch: statsQuery.refetch
    },
    salesChart: {
      data: salesChartQuery.data?.data,
      isLoading: salesChartQuery.isLoading,
      error: salesChartQuery.error,
      refetch: salesChartQuery.refetch
    },
    recentOrders: {
      data: recentOrdersQuery.data?.data,
      isLoading: recentOrdersQuery.isLoading,
      error: recentOrdersQuery.error,
      refetch: recentOrdersQuery.refetch
    },
    topProducts: {
      data: topProductsQuery.data?.data,
      isLoading: topProductsQuery.isLoading,
      error: topProductsQuery.error,
      refetch: topProductsQuery.refetch
    },
    // Trạng thái tổng hợp
    isLoading: statsQuery.isLoading || salesChartQuery.isLoading || 
               recentOrdersQuery.isLoading || topProductsQuery.isLoading,
    hasError: statsQuery.error || salesChartQuery.error || 
              recentOrdersQuery.error || topProductsQuery.error,
    refetchAll: () => {
      statsQuery.refetch();
      salesChartQuery.refetch();
      recentOrdersQuery.refetch();
      topProductsQuery.refetch();
    }
  };
};
