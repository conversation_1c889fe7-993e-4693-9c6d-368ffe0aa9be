'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Insert customer groups
    await queryInterface.bulkInsert('nhom_khach_hang', [
      {
        id: 1,
        ten_nhom: '<PERSON>h<PERSON>ch hàng VIP',
        ma_nhom: 'VIP',
        mo_ta: '<PERSON>h<PERSON>ch hàng có tổng chi tiêu từ 10,000,000 VND trở lên',
        loai_nhom: 1, // cố định
        ngay_tao: new Date()
      },
      {
        id: 2,
        ten_nhom: 'Khách hàng Thân thiết',
        ma_nhom: 'LOYAL',
        mo_ta: '<PERSON>h<PERSON>ch hàng có tổng chi tiêu từ 5,000,000 - 9,999,999 VND',
        loai_nhom: 1, // cố định
        ngay_tao: new Date()
      },
      {
        id: 3,
        ten_nhom: '<PERSON>h<PERSON><PERSON> hàng Thườ<PERSON> xuyên',
        ma_nhom: 'REGULAR',
        mo_ta: '<PERSON>h<PERSON><PERSON> hàng có tổng chi tiêu từ 1,000,000 - 4,999,999 VND',
        loai_nhom: 1, // cố định
        ngay_tao: new Date()
      },
      {
        id: 4,
        ten_nhom: 'Khách hàng Mới',
        ma_nhom: 'NEW',
        mo_ta: 'Khách hàng có tổng chi tiêu dưới 1,000,000 VND',
        loai_nhom: 1, // cố định
        ngay_tao: new Date()
      },
      {
        id: 5,
        ten_nhom: 'Khách hàng Doanh nghiệp',
        ma_nhom: 'BUSINESS',
        mo_ta: 'Khách hàng là doanh nghiệp, công ty',
        loai_nhom: 1, // cố định
        ngay_tao: new Date()
      },
      {
        id: 6,
        ten_nhom: 'Khách hàng Bán lẻ',
        ma_nhom: 'RETAIL',
        mo_ta: 'Khách hàng mua lẻ, cá nhân',
        loai_nhom: 1, // cố định
        ngay_tao: new Date()
      }
    ], {});

    // Check if customers exist first, then assign to groups
    const customerIds = await queryInterface.sequelize.query(
      "SELECT id FROM nguoi_dung WHERE loai_nguoi_dung = 'khach_hang' LIMIT 10",
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    if (customerIds.length > 0) {
      const assignments = [];

      // Assign customers to groups based on available customer IDs
      customerIds.forEach((customer, index) => {
        const groupId = (index % 4) + 1; // Distribute among groups 1-4
        assignments.push({
          nguoi_dung_id: customer.id,
          nhom_khach_hang_id: groupId
        });

        // Also assign to retail group (group 6)
        assignments.push({
          nguoi_dung_id: customer.id,
          nhom_khach_hang_id: 6
        });
      });

      if (assignments.length > 0) {
        await queryInterface.bulkInsert('nhom_khach_hang_nguoi_dung', assignments, {});
      }
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('nhom_khach_hang_nguoi_dung', null, {});
    await queryInterface.bulkDelete('nhom_khach_hang', null, {});
  }
};
