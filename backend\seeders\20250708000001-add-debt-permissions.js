'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Thêm permissions cho công nợ
    const debtPermissions = [
      { ma_quyen: 'XEM_CONG_NO', ten_quyen: 'Xem công nợ', mo_ta: 'Quyền xem danh sách công nợ khách hàng' },
      { ma_quyen: 'SUA_CONG_NO', ten_quyen: 'Sửa công nợ', mo_ta: 'Quyền chỉnh sửa thông tin công nợ' },
      { ma_quyen: 'TAO_PHIEU_THU', ten_quyen: 'Tạo phiếu thu', mo_ta: 'Quyền tạo phiếu thu tiền' },
      { ma_quyen: 'TAO_PHIEU_CHI', ten_quyen: 'Tạo phiếu chi', mo_ta: 'Quyền tạo phiếu chi tiền' },
      { ma_quyen: 'XEM_BAO_CAO_CONG_NO', ten_quyen: 'Xem báo cáo công nợ', mo_ta: 'Quyền xem báo cáo công nợ' }
    ];

    // Kiểm tra xem permissions đã tồn tại chưa
    for (const permission of debtPermissions) {
      const existingPermission = await queryInterface.sequelize.query(
        `SELECT id FROM quyen WHERE ma_quyen = '${permission.ma_quyen}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingPermission.length === 0) {
        await queryInterface.bulkInsert('quyen', [permission], {});
        console.log(`✅ Added permission: ${permission.ma_quyen}`);
      } else {
        console.log(`⚠️ Permission already exists: ${permission.ma_quyen}`);
      }
    }

    // Gán quyền công nợ cho role ADMIN
    const adminRole = await queryInterface.sequelize.query(
      `SELECT id FROM vai_tro WHERE ma_vai_tro = 'ADMIN'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (adminRole.length > 0) {
      const adminRoleId = adminRole[0].id;
      
      for (const permission of debtPermissions) {
        const permissionRecord = await queryInterface.sequelize.query(
          `SELECT id FROM quyen WHERE ma_quyen = '${permission.ma_quyen}'`,
          { type: Sequelize.QueryTypes.SELECT }
        );

        if (permissionRecord.length > 0) {
          const permissionId = permissionRecord[0].id;
          
          // Kiểm tra xem role-permission đã tồn tại chưa
          const existingRolePermission = await queryInterface.sequelize.query(
            `SELECT * FROM vai_tro_quyen WHERE vai_tro_id = ${adminRoleId} AND quyen_id = ${permissionId}`,
            { type: Sequelize.QueryTypes.SELECT }
          );

          if (existingRolePermission.length === 0) {
            await queryInterface.bulkInsert('vai_tro_quyen', [{
              vai_tro_id: adminRoleId,
              quyen_id: permissionId
            }], {});
            console.log(`✅ Assigned permission ${permission.ma_quyen} to ADMIN role`);
          } else {
            console.log(`⚠️ Permission ${permission.ma_quyen} already assigned to ADMIN role`);
          }
        }
      }
    }

    // Gán một số quyền công nợ cho role QUAN_LY
    const managerRole = await queryInterface.sequelize.query(
      `SELECT id FROM vai_tro WHERE ma_vai_tro = 'QUAN_LY'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (managerRole.length > 0) {
      const managerRoleId = managerRole[0].id;
      const managerDebtPermissions = ['XEM_CONG_NO', 'TAO_PHIEU_THU', 'XEM_BAO_CAO_CONG_NO'];
      
      for (const permissionCode of managerDebtPermissions) {
        const permissionRecord = await queryInterface.sequelize.query(
          `SELECT id FROM quyen WHERE ma_quyen = '${permissionCode}'`,
          { type: Sequelize.QueryTypes.SELECT }
        );

        if (permissionRecord.length > 0) {
          const permissionId = permissionRecord[0].id;
          
          // Kiểm tra xem role-permission đã tồn tại chưa
          const existingRolePermission = await queryInterface.sequelize.query(
            `SELECT * FROM vai_tro_quyen WHERE vai_tro_id = ${managerRoleId} AND quyen_id = ${permissionId}`,
            { type: Sequelize.QueryTypes.SELECT }
          );

          if (existingRolePermission.length === 0) {
            await queryInterface.bulkInsert('vai_tro_quyen', [{
              vai_tro_id: managerRoleId,
              quyen_id: permissionId
            }], {});
            console.log(`✅ Assigned permission ${permissionCode} to QUAN_LY role`);
          } else {
            console.log(`⚠️ Permission ${permissionCode} already assigned to QUAN_LY role`);
          }
        }
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // Xóa role-permissions cho công nợ
    await queryInterface.sequelize.query(`
      DELETE vt FROM vai_tro_quyen vt
      INNER JOIN quyen q ON vt.quyen_id = q.id
      WHERE q.ma_quyen IN ('XEM_CONG_NO', 'SUA_CONG_NO', 'TAO_PHIEU_THU', 'TAO_PHIEU_CHI', 'XEM_BAO_CAO_CONG_NO')
    `);

    // Xóa permissions công nợ
    await queryInterface.bulkDelete('quyen', {
      ma_quyen: ['XEM_CONG_NO', 'SUA_CONG_NO', 'TAO_PHIEU_THU', 'TAO_PHIEU_CHI', 'XEM_BAO_CAO_CONG_NO']
    }, {});
  }
};
