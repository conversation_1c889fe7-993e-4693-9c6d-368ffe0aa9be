'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('dia_chi_nguoi_dung', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      nguoi_dung_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      dia_chi: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      phuong_xa: {
        type: Sequelize.STRING,
        allowNull: true
      },
      quan_huyen: {
        type: Sequelize.STRING,
        allowNull: true
      },
      tinh_thanh: {
        type: Sequelize.STRING,
        allowNull: true
      },
      mac_dinh: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      }
    });

    // Add indexes
    await queryInterface.addIndex('dia_chi_nguoi_dung', ['nguoi_dung_id']);
    await queryInterface.addIndex('dia_chi_nguoi_dung', ['tinh_thanh']);
    await queryInterface.addIndex('dia_chi_nguoi_dung', ['quan_huyen']);
    await queryInterface.addIndex('dia_chi_nguoi_dung', ['mac_dinh']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('dia_chi_nguoi_dung');
  }
};
