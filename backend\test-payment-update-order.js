require('dotenv').config();
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Payment, CongNoNguoiDung, sequelize } = require('./models');

async function testPaymentUpdateOrder() {
  try {
    console.log('🧪 Testing Payment Update Order tong_da_tra...\n');

    // 1. Tạo khách hàng test
    console.log('1️⃣ Creating test customer...');
    const customer = await NguoiDung.create({
      ho_ten: 'Nguyễn Văn Test Payment',
      so_dien_thoai: '0987654777',
      email: '<EMAIL>',
      loai_nguoi_dung: 'khach_hang',
      trang_thai: 'dang_giao_dich'
    });
    console.log(`✅ Created customer: ${customer.ho_ten} (ID: ${customer.id})`);

    // 2. Tạo đơn hàng có tiền COD
    console.log('\n2️⃣ Creating order with COD...');
    const order = await DonHang.create({
      ma_don_hang: `PAYMENT-TEST-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'da_xac_nhan',
      tong_tien: 1000000, // Tổng tiền sản phẩm
      tong_phai_tra: 500000, // Tiền COD
      tong_da_tra: 0, // Chưa thanh toán
      con_phai_tra: 500000, // Còn nợ
      tien_cod: 500000, // Tiền COD
      tien_coc: 0, // Tiền cọc
      ghi_chu: 'Test order for payment update'
    });
    console.log(`✅ Created order: ${order.ma_don_hang}`);
    console.log(`   - tong_da_tra (before): ${order.tong_da_tra?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_cod: ${order.tien_cod?.toLocaleString('vi-VN')}đ`);

    // 3. Tạo bản ghi công nợ
    console.log('\n3️⃣ Creating debt record...');
    const debtRecord = await CongNoNguoiDung.create({
      nguoi_dung_id: customer.id,
      tong_cong_no: 500000, // Công nợ ban đầu
    });
    console.log(`✅ Created debt record: ${debtRecord.tong_cong_no?.toLocaleString('vi-VN')}đ`);

    // 4. Simulate payment API call
    console.log('\n4️⃣ Simulating payment API call...');
    
    const paymentData = {
      customer_id: customer.id,
      type: 'thu',
      amount: 200000,
      payment_method: 'cash',
      note: 'Test payment for order',
      order_id: order.id, // Quan trọng: có order_id
    };

    console.log('📤 Payment data:', paymentData);

    // Simulate createPayment logic
    const customerId = parseInt(paymentData.customer_id);
    const paymentAmount = parseFloat(paymentData.amount);

    // Lấy thông tin khách hàng và công nợ hiện tại
    const customerWithDebt = await NguoiDung.findByPk(customerId, {
      include: [
        {
          model: CongNoNguoiDung,
          as: "congNo",
        },
      ],
    });

    const currentDebtRecord = customerWithDebt.congNo;
    const previousDebt = parseFloat(currentDebtRecord.tong_cong_no || 0);
    const newDebtAmount = previousDebt - paymentAmount; // Thu tiền -> giảm công nợ

    // Cập nhật công nợ
    await currentDebtRecord.update({
      tong_cong_no: newDebtAmount,
    });

    // Lưu payment
    const savedPayment = await Payment.create({
      customer_id: customerId,
      type: paymentData.type,
      amount: paymentAmount,
      payment_method: paymentData.payment_method || "cash",
      note: paymentData.note || "",
      created_by: "test",
      status: "completed",
    });

    // ✅ LOGIC MỚI: Cập nhật tong_da_tra trong đơn hàng
    let orderUpdate = null;
    if (paymentData.order_id && paymentData.type === "thu") {
      const orderToUpdate = await DonHang.findByPk(paymentData.order_id);
      if (orderToUpdate) {
        const currentPaid = orderToUpdate.tong_da_tra || 0;
        const newPaidAmount = currentPaid + paymentAmount;
        
        await orderToUpdate.update({
          tong_da_tra: newPaidAmount,
        });
        
        orderUpdate = {
          order_id: paymentData.order_id,
          previous_paid: currentPaid,
          payment_amount: paymentAmount,
          new_paid_amount: newPaidAmount,
        };
        
        console.log(`📦 Updated order ${paymentData.order_id} tong_da_tra: ${currentPaid} -> ${newPaidAmount}`);
      }
    }

    console.log('✅ Payment processed successfully');

    // 5. Verify results
    console.log('\n5️⃣ Verifying results...');
    
    // Kiểm tra đơn hàng đã được cập nhật
    const updatedOrder = await DonHang.findByPk(order.id);
    console.log('📋 Order after payment:');
    console.log(`   - tong_da_tra (after): ${updatedOrder.tong_da_tra?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_cod: ${updatedOrder.tien_cod?.toLocaleString('vi-VN')}đ`);
    
    // Tính toán COD còn lại
    const remainingCOD = (updatedOrder.tien_cod || 0) - (updatedOrder.tong_da_tra || 0);
    console.log(`   - Remaining COD: ${remainingCOD.toLocaleString('vi-VN')}đ`);

    // Kiểm tra công nợ đã được cập nhật
    const updatedDebtRecord = await CongNoNguoiDung.findOne({
      where: { nguoi_dung_id: customer.id }
    });
    console.log('💰 Debt after payment:');
    console.log(`   - Previous debt: ${previousDebt.toLocaleString('vi-VN')}đ`);
    console.log(`   - Payment amount: ${paymentAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - New debt: ${updatedDebtRecord.tong_cong_no?.toLocaleString('vi-VN')}đ`);

    // Kiểm tra payment record
    console.log('📄 Payment record:');
    console.log(`   - ID: ${savedPayment.id}`);
    console.log(`   - Amount: ${savedPayment.amount?.toLocaleString('vi-VN')}đ`);
    console.log(`   - Type: ${savedPayment.type}`);
    console.log(`   - Status: ${savedPayment.status}`);

    // 6. Test Frontend calculation
    console.log('\n6️⃣ Testing Frontend calculation...');
    
    const frontendCalc = {
      codAmount: updatedOrder.tien_cod || 0,
      paidAmount: updatedOrder.tong_da_tra || 0,
      remainingCOD: (updatedOrder.tien_cod || 0) - (updatedOrder.tong_da_tra || 0),
      paymentStatus: (updatedOrder.tien_cod || 0) - (updatedOrder.tong_da_tra || 0) <= 0 
        ? 'Đã thanh toán đủ COD' 
        : `Còn COD: ${((updatedOrder.tien_cod || 0) - (updatedOrder.tong_da_tra || 0)).toLocaleString('vi-VN')}đ`,
      canPay: (updatedOrder.tien_cod || 0) - (updatedOrder.tong_da_tra || 0) > 0
    };

    console.log('🖥️ Frontend Display:');
    console.log(`   - Tiền COD: ${frontendCalc.codAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - Đã thanh toán: ${frontendCalc.paidAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - Còn COD: ${frontendCalc.remainingCOD.toLocaleString('vi-VN')}đ`);
    console.log(`   - Payment Status: ${frontendCalc.paymentStatus}`);
    console.log(`   - Can Pay: ${frontendCalc.canPay}`);

    // 7. Cleanup
    console.log('\n7️⃣ Cleaning up...');
    await savedPayment.destroy();
    await updatedDebtRecord.destroy();
    await updatedOrder.destroy();
    await customer.destroy();
    console.log('✅ Cleanup completed');

    // 8. Final verification
    console.log('\n8️⃣ Final Verification:');
    const expectedPaid = 200000;
    const expectedRemaining = 300000;
    
    if (updatedOrder.tong_da_tra === expectedPaid && remainingCOD === expectedRemaining) {
      console.log('🎉 All tests passed! Payment now updates tong_da_tra correctly.');
      console.log('✅ Frontend will display correct payment amounts.');
      console.log('✅ COD calculation works perfectly.');
    } else {
      console.log('❌ Tests failed!');
      console.log(`   Expected paid: ${expectedPaid}, Got: ${updatedOrder.tong_da_tra}`);
      console.log(`   Expected remaining: ${expectedRemaining}, Got: ${remainingCOD}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy test
testPaymentUpdateOrder();
