# ===== Stage 1: Builder =====
FROM node:18-alpine AS builder

# Cài curl để dùng healthcheck
RUN apk add --no-cache curl

# Thư mục làm việc
WORKDIR /usr/src/app

# Copy file package.json và lock
COPY package*.json ./

# Cài dependency cho production
RUN npm ci --only=production

# ===== Stage 2: Production =====
FROM node:18-alpine AS production

# Cài curl, bash và netcat để dùng wait-for-it.sh
RUN apk add --no-cache curl bash netcat-openbsd

# Thư mục làm việc
WORKDIR /usr/src/app

# Tạo user non-root
RUN addgroup -g 1001 -S nodejs && adduser -S backend -u 1001

# Copy dependencies từ builder
COPY --from=builder /usr/src/app/node_modules ./node_modules

# Copy toàn bộ source code
COPY . .

# Tạo script wait-for-it.sh nội bộ
RUN echo '#!/bin/bash\n\
TIMEOUT=15\n\
QUIET=0\n\
HOST=$1\n\
PORT=$2\n\
shift 2\n\
CMD="$@"\n\
\n\
until nc -z $HOST $PORT; do\n\
  >&2 echo "Waiting for $HOST:$PORT..."\n\
  sleep 1\n\
done\n\
\n\
>&2 echo "$HOST:$PORT is available"\n\
exec $CMD' > /usr/local/bin/wait-for-it.sh && \
    chmod +x /usr/local/bin/wait-for-it.sh

# Chuyển quyền sở hữu
RUN chown -R backend:nodejs /usr/src/app
USER backend

# Mở cổng backend
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:5000/api/health || exit 1

# Lệnh chạy cuối cùng (chạy qua wait-for-it)
CMD ["npm", "start"]
