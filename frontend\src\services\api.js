import axios from "axios";
import { message } from "antd";
import Cookies from "js-cookie";

// API Configuration
export const API_CONFIG = {
  // BASE_URL: import.meta.env.VITE_API_URL || "http://localhost:5000/api",
  BASE_URL: import.meta.env.VITE_API_URL || "http://***************:5000/api",
  TIMEOUT: 10000,
};

// Helper function to build API URLs
export const buildApiUrl = (endpoint) => {
  return `${API_CONFIG.BASE_URL}${
    endpoint.startsWith("/") ? endpoint : "/" + endpoint
  }`;
};

// Helper function for fetch requests
export const apiRequest = async (url, options = {}) => {
  const response = await fetch(url, {
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
};

// Create axios instance
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          Cookies.remove("token");
          if (window.location.pathname !== "/login") {
            message.error("Phiên đăng nhập đã hết hạn");
            window.location.href = "/login";
          }
          break;

        case 403:
          // Forbidden
          message.error(
            data.message || "Bạn không có quyền thực hiện hành động này"
          );
          break;

        case 404:
          // Not found
          message.error(data.message || "Không tìm thấy tài nguyên");
          break;

        case 422:
          // Validation error
          if (data.errors && Array.isArray(data.errors)) {
            data.errors.forEach((err) => {
              message.error(`${err.field}: ${err.message}`);
            });
          } else {
            message.error(data.message || "Dữ liệu không hợp lệ");
          }
          break;

        case 500:
          // Server error
          message.error("Lỗi máy chủ, vui lòng thử lại sau");
          break;

        default:
          message.error(data.message || "Có lỗi xảy ra");
      }
    } else if (error.request) {
      // Network error
      message.error("Không thể kết nối đến máy chủ");
    } else {
      // Other error
      message.error("Có lỗi xảy ra");
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post("/auth/login", credentials),
  register: (userData) => api.post("/auth/register", userData),
  getMe: () => api.get("/auth/me"),
  changePassword: (passwordData) =>
    api.post("/auth/change-password", passwordData),
};

// Users API
export const usersAPI = {
  getUsers: (params) => api.get("/users", { params }),
  getUser: (id) => api.get(`/users/${id}`),
  createUser: (userData) => api.post("/users", userData),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  assignRoles: (id, roleIds) =>
    api.post(`/users/${id}/assign-roles`, { vai_tro_ids: roleIds }),
};

// Roles API
export const rolesAPI = {
  getRoles: () => api.get("/roles"),
  getRole: (id) => api.get(`/roles/${id}`),
  createRole: (roleData) => api.post("/roles", roleData),
  updateRole: (id, roleData) => api.put(`/roles/${id}`, roleData),
  deleteRole: (id) => api.delete(`/roles/${id}`),
  assignPermissions: (id, permissionIds) =>
    api.post(`/roles/${id}/assign-permissions`, { quyen_ids: permissionIds }),
};

// Permissions API
export const permissionsAPI = {
  getPermissions: () => api.get("/permissions"),
  getPermission: (id) => api.get(`/permissions/${id}`),
  createPermission: (permissionData) =>
    api.post("/permissions", permissionData),
  updatePermission: (id, permissionData) =>
    api.put(`/permissions/${id}`, permissionData),
  deletePermission: (id) => api.delete(`/permissions/${id}`),
};

// Products API
export const productsAPI = {
  getProducts: (params) => api.get("/products", { params }),
  getProduct: (id) => api.get(`/products/${id}`),
  createProduct: (productData) => api.post("/products", productData),
  updateProduct: (id, productData) => api.put(`/products/${id}`, productData),
  deleteProduct: (id) => api.delete(`/products/${id}`),

  // Product Categories
  getCategories: () => api.get("/products/categories"),
  getCategory: (id) => api.get(`/products/categories/${id}`),
  createCategory: (categoryData) =>
    api.post("/products/categories", categoryData),
  updateCategory: (id, categoryData) =>
    api.put(`/products/categories/${id}`, categoryData),
  deleteCategory: (id) => api.delete(`/products/categories/${id}`),

  // Product Brands
  getBrands: () => api.get("/products/brands"),
  getBrand: (id) => api.get(`/products/brands/${id}`),
  createBrand: (brandData) => api.post("/products/brands", brandData),
  updateBrand: (id, brandData) => api.put(`/products/brands/${id}`, brandData),
  deleteBrand: (id) => api.delete(`/products/brands/${id}`),

  // Product Tags
  getTags: () => api.get("/products/tags"),
  getTag: (id) => api.get(`/products/tags/${id}`),
  createTag: (tagData) => api.post("/products/tags", tagData),
  updateTag: (id, tagData) => api.put(`/products/tags/${id}`, tagData),
  deleteTag: (id) => api.delete(`/products/tags/${id}`),

  // Product Attributes
  getAttributes: () => api.get("/products/attributes"),
  getAttribute: (id) => api.get(`/products/attributes/${id}`),
  createAttribute: (attributeData) =>
    api.post("/products/attributes", attributeData),
  updateAttribute: (id, attributeData) =>
    api.put(`/products/attributes/${id}`, attributeData),
  deleteAttribute: (id) => api.delete(`/products/attributes/${id}`),

  // Product Variants
  getVariants: (productId) => api.get(`/products/${productId}/variants`),
  getVariant: (productId, variantId) =>
    api.get(`/products/${productId}/variants/${variantId}`),
  createVariant: (productId, variantData) =>
    api.post(`/products/${productId}/variants`, variantData),
  updateVariant: (productId, variantId, variantData) =>
    api.put(`/products/${productId}/variants/${variantId}`, variantData),
  deleteVariant: (productId, variantId) =>
    api.delete(`/products/${productId}/variants/${variantId}`),
};

// Orders API
export const ordersAPI = {
  getOrders: (params) => api.get("/orders", { params }),
  getOrder: (id) => api.get(`/orders/${id}`),
  createOrder: (orderData) => api.post("/orders", orderData),
  updateOrder: (id, orderData) => api.put(`/orders/${id}`, orderData),
  deleteOrder: (id) => api.delete(`/orders/${id}`),

  // Order Details
  getOrderDetails: (id) => api.get(`/orders/${id}/details`),

  // Order Status Management
  updateOrderStatus: (id, status) =>
    api.put(`/orders/${id}/status`, { trang_thai: status }),
  cancelOrder: (id) => api.put(`/orders/${id}/cancel`),
  completeOrder: (id) => api.put(`/orders/${id}/complete`),
  returnOrder: (id, returnData) => api.post(`/orders/${id}/return`, returnData),

  // Order Statistics
  getOrderStats: (params) => api.get("/orders/stats", { params }),
};

// Production API endpoints (using proper authentication and routes)
export const testAPI = {
  // Orders
  getOrders: (params) => {
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(
        ([_, value]) =>
          value !== undefined && value !== "undefined" && value !== ""
      )
    );
    return api.get("/orders", { params: cleanParams }).then((res) => res.data);
  },

  getOrder: (id) => {
    return api.get(`/orders/${id}`).then((res) => res.data);
  },

  createOrder: (orderData) => {
    return api.post("/orders", orderData).then((res) => res.data);
  },

  updateOrder: (id, orderData) => {
    return api.put(`/orders/${id}`, orderData).then((res) => res.data);
  },

  updateOrderStatus: (id, status) => {
    return api
      .put(`/orders/${id}/status`, { trang_thai: status })
      .then((res) => res.data);
  },

  returnOrder: (id, returnData) => {
    return api.post(`/orders/${id}/return`, returnData).then((res) => res.data);
  },

  // Products
  getProducts: (params) => {
    return api.get("/products", { params }).then((res) => res.data);
  },

  getProductVariants: (params) => {
    return api
      .get("/products/variants/search", { params })
      .then((res) => res.data);
  },

  getProductStockHistory: (productId, params) => {
    return api
      .get(`/warehouses/products/${productId}/stock-history`, { params })
      .then((res) => res.data);
  },

  getVariantStockHistory: (variantId, params) => {
    return api
      .get(`/warehouses/variants/${variantId}/stock-history`, { params })
      .then((res) => res.data);
  },

  // Categories
  getCategories: () => {
    return api.get("/products/categories").then((res) => res.data);
  },

  createCategory: (data) => {
    return api.post("/products/categories", data).then((res) => res.data);
  },

  updateCategory: (id, data) => {
    return api.put(`/products/categories/${id}`, data).then((res) => res.data);
  },

  deleteCategory: (id) => {
    return api.delete(`/products/categories/${id}`).then((res) => res.data);
  },

  // Brands
  getBrands: () => {
    return api.get("/products/brands").then((res) => res.data);
  },

  createBrand: (data) => {
    return api.post("/products/brands", data).then((res) => res.data);
  },

  updateBrand: (id, data) => {
    return api.put(`/products/brands/${id}`, data).then((res) => res.data);
  },

  deleteBrand: (id) => {
    return api.delete(`/products/brands/${id}`).then((res) => res.data);
  },

  // Customers
  getCustomers: (params) => {
    return api.get("/customers", { params }).then((res) => res.data);
  },

  // Payments
  createPayment: (paymentData) => {
    return api.post("/debt/payment", paymentData).then((res) => res.data);
  },

  // Debt Management
  getDebtList: (params) => {
    return api.get("/debt", { params }).then((res) => res.data);
  },

  getCustomerDebtDetail: (customerId) => {
    return api.get(`/debt/customer/${customerId}`).then((res) => res.data);
  },

  getDebtReport: () => {
    return api.get("/debt/report").then((res) => res.data);
  },

  syncDebtFromOrders: () => {
    return api.post("/debt/sync").then((res) => res.data);
  },

  // Internal Notes
  getInternalNotes: (customerId) => {
    return api.get(`/debt/notes/${customerId}`).then((res) => res.data);
  },

  createInternalNote: (noteData) => {
    return api.post("/debt/notes", noteData).then((res) => res.data);
  },

  // Stock Checks
  getStockChecks: (params) => {
    return api.get("/stock-checks", { params }).then((res) => res.data);
  },

  createStockCheck: (data) => {
    return api.post("/stock-checks", data).then((res) => res.data);
  },

  getStockCheckDetails: (id) => {
    return api.get(`/stock-checks/${id}/details`).then((res) => res.data);
  },

  addProductToStockCheck: (id, data) => {
    return api
      .post(`/stock-checks/${id}/products`, data)
      .then((res) => res.data);
  },

  getAvailableProducts: (id, params) => {
    return api
      .get(`/stock-checks/${id}/available-products`, { params })
      .then((res) => res.data);
  },

  // Dashboard
  getDashboardStats: () => {
    return api.get("/dashboard/stats").then((res) => res.data);
  },

  getSalesChart: () => {
    return api.get("/dashboard/sales-chart").then((res) => res.data);
  },

  getRecentOrders: () => {
    return api.get("/dashboard/recent-orders").then((res) => res.data);
  },

  getTopProducts: () => {
    return api.get("/dashboard/top-products").then((res) => res.data);
  },

  // Reports
  getOverviewReport: (params) => {
    return api.get("/reports/overview", { params }).then((res) => res.data);
  },

  getProductReport: (params) => {
    return api.get("/reports/products", { params }).then((res) => res.data);
  },

  getCustomerReport: (params) => {
    return api.get("/reports/customers", { params }).then((res) => res.data);
  },

  getBusinessActivityReport: (params) => {
    return api
      .get("/reports/business-activity", { params })
      .then((res) => res.data);
  },

  getCustomerGroupReport: (params) => {
    return api
      .get("/reports/customer-groups", { params })
      .then((res) => res.data);
  },
};

// Warehouses API
export const warehousesAPI = {
  getWarehouses: (params) => api.get("/warehouses", { params }),
  getWarehouse: (id) => api.get(`/warehouses/${id}`),
  createWarehouse: (warehouseData) => api.post("/warehouses", warehouseData),
  updateWarehouse: (id, warehouseData) =>
    api.put(`/warehouses/${id}`, warehouseData),
  deleteWarehouse: (id) => api.delete(`/warehouses/${id}`),

  // Inventory Management
  getInventory: (params) => api.get("/warehouses/inventory", { params }),
  getInventoryByWarehouse: (warehouseId, params) =>
    api.get(`/warehouses/${warehouseId}/inventory`, { params }),
  adjustInventory: (adjustmentData) =>
    api.post("/warehouses/inventory/adjust", adjustmentData),

  // Stock Movements
  getStockMovements: (params) => api.get("/warehouses/movements", { params }),
  getStockMovement: (id) => api.get(`/warehouses/movements/${id}`),
  createStockMovement: (movementData) =>
    api.post("/warehouses/movements", movementData),

  // Stock Checks
  getStockChecks: (params) => api.get("/warehouses/stock-checks", { params }),
  getStockCheck: (id) => api.get(`/warehouses/stock-checks/${id}`),
  createStockCheck: (checkData) =>
    api.post("/warehouses/stock-checks", checkData),
  updateStockCheck: (id, checkData) =>
    api.put(`/warehouses/stock-checks/${id}`, checkData),
  deleteStockCheck: (id) => api.delete(`/warehouses/stock-checks/${id}`),

  // Stock Check Details
  getStockCheckDetails: (id, params) =>
    api.get(`/warehouses/stock-checks/${id}/details`, { params }),
  updateStockCheckItem: (stockCheckId, itemId, data) =>
    api.put(`/warehouses/stock-checks/${stockCheckId}/items/${itemId}`, data),
  completeStockCheck: (id) =>
    api.post(`/warehouses/stock-checks/${id}/complete`),

  // Stock Check Products
  getAvailableProducts: (id, params) =>
    api.get(`/warehouses/stock-checks/${id}/available-products`, { params }),
  addProductToStockCheck: (id, data) =>
    api.post(`/warehouses/stock-checks/${id}/products`, data),
  removeProductFromStockCheck: (stockCheckId, itemId) =>
    api.delete(`/warehouses/stock-checks/${stockCheckId}/items/${itemId}`),
};

//Customer API
export const customerAPI = {
  getAllCustomerGroup: (params) => api.get("/customer-groups", { params }),
  getCustomerGroups: () => api.get("/customer-groups/all"),
  createCustomerGroup: (data) => api.post("/customer-groups", data),
  updateCustomerGroup: (id, data) => api.put(`/customer-groups/${id}`, data),
  deleteCustomerGroup: (id) => api.delete(`/customer-groups/${id}`),

  getCustomers: (params) => api.get("/customers", { params }),
  getCustomer: (id) => api.get(`/customers/${id}`),
  createCustomer: (customerData) => api.post("/customers", customerData),
  updateCustomer: (id, customerData) =>
    api.put(`/customers/${id}`, customerData),
  deleteCustomer: (id) => api.delete(`/customers/${id}`),
  exportCustomers: () => api.get("/customers/export", { responseType: "blob" }),
  importCustomers: (formData) =>
    api.post("/customers/import", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    }),
};

// Customers API (alias for consistency)
export const customersAPI = customerAPI;

// Health check API
export const healthAPI = {
  check: () => api.get("/health"),
};

// Reports API
export const reportsAPI = {
  getOverviewReport: (params) => api.get("/reports/overview", { params }),
  getProductReport: (params) => api.get("/reports/products", { params }),
  getCustomerReport: (params) => api.get("/reports/customers", { params }),
  getBusinessActivityReport: (params) =>
    api.get("/reports/business-activity", { params }),
  getCustomerGroupReport: (params) =>
    api.get("/reports/customer-groups", { params }),
};

// Filter Data API - Lấy dữ liệu thật cho bộ lọc
export const filterDataAPI = {
  // Lấy danh sách nhóm khách hàng
  getCustomerGroups: () => api.get("/customer-groups/all"),

  // Lấy danh sách khách hàng
  getCustomers: (params) => api.get("/customers", { params }),

  // Lấy danh mục sản phẩm
  getProductCategories: () => api.get("/products/categories"),

  // Lấy nhãn hiệu
  getBrands: () => api.get("/products/brands"),

  // Lấy tags
  getTags: () => api.get("/tags"),

  // Lấy danh sách kho hàng
  getWarehouses: () => api.get("/warehouses"),
};

export default api;
