const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');
const {
  getDashboardStats,
  getSalesChart,
  getRecentOrders,
  getTopProducts
} = require('../controllers/dashboardController');

const router = express.Router();

/**
 * @route GET /api/dashboard/stats
 * @desc Lấy thống kê tổng quan dashboard
 * @access Private (Cần quyền XEM_DASHBOARD)
 */
router.get('/stats', asyncHandler(getDashboardStats));

/**
 * @route GET /api/dashboard/sales-chart
 * @desc Lấy dữ liệu biểu đồ doanh thu 7 ngày
 * @access Private
 */
router.get('/sales-chart', asyncHandler(getSalesChart));

/**
 * @route GET /api/dashboard/recent-orders
 * @desc Lấy danh sách đơn hàng gần đây
 * @access Private
 */
router.get('/recent-orders', asyncHandler(getRecentOrders));

/**
 * @route GET /api/dashboard/top-products
 * @desc Lấy danh sách sản phẩm bán chạy
 * @access Private
 */
router.get('/top-products', asyncHandler(getTopProducts));

module.exports = router;
