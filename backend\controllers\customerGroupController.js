const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NhomKhachHang<PERSON>gu<PERSON>, NguoiDung, sequelize } = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');

const getCustomerGroups = async (req, res) => {
  try {
    // Lấy từ req.query thay vì req.params
    const current = Math.max(1, parseInt(req.query.current) || 1);
    const pageSize = Math.max(1, parseInt(req.query.pageSize) || 10);
    const offset = (current - 1) * pageSize;
        
    const whereClause = {};

    // First get total count
    const totalCount = await NhomKhachHang.count({ where: whereClause });

    // Then get the data with user counts using a subquery
    const customerGroups = await NhomKhachHang.findAll({
      where: whereClause,
      attributes: [
        'id',
        'ten_nhom',
        'mo_ta', 
        'ma_nhom',
        'ngay_tao',
        'loai_nhom', 
        [
          sequelize.literal(`(
            SELECT COUNT(*)
            FROM nhom_khach_hang_nguoi_dung nkhnd
            WHERE nkhnd.nhom_khach_hang_id = NhomKhachHang.id
          )`),
          'so_luong_khach_hang'
        ]
      ],
      limit: pageSize,
      offset: offset,
      order: [['id', 'DESC']],
    });

    // Transform data
    const customerGroupsData = customerGroups.map(customerGroup => {
      const data = customerGroup.toJSON();
      return {
        id: data.id,
        ten_nhom: data.ten_nhom || null,
        mo_ta: data.mo_ta || null,
        ma_nhom: data.ma_nhom || null,
        ngay_tao: data.ngay_tao,
        loai_nhom: data.loai_nhom,
        so_luong_khach_hang: parseInt(data.so_luong_khach_hang) || 0,
      };
    });

    res.json({
      success: true,
      data: customerGroupsData,
      pagination: {
        total: totalCount,
        current,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize),
      },
    });
  } catch (error) {
    console.error('Error in getCustomerGroups:', error);
    throw new AppError(`Failed to fetch customer groups: ${error.message}`, 500);
  }
};
const getAllCustomerGroupsNoPagin = async (req, res) => {
  try {
    const customerGroups = await NhomKhachHang.findAll({
      attributes: [
        'id',
        'ten_nhom',
        'mo_ta', 
        'ma_nhom',
        'loai_nhom', 
      ],
      order: [['id', 'DESC']],
    });

    // Transform data
    const customerGroupsData = customerGroups.map(customerGroup => {
      const data = customerGroup.toJSON();
      return {
        id: data.id,
        ten_nhom: data.ten_nhom || null,
        mo_ta: data.mo_ta || null,
        ma_nhom: data.ma_nhom || null,
        loai_nhom: data.loai_nhom,
      };
    });

    res.json({
      success: true,
      data: customerGroupsData,
    });
  } catch (error) {
    console.error('Error in getCustomerGroups:', error);
    throw new AppError(`Failed to fetch customer groups: ${error.message}`, 500);
  }
};
const createCustomerGroup = async (req, res) => {
  try {
    const { ten_nhom, mo_ta, ma_nhom, loai_nhom } = req.body;

    // Validate required fields
    if (!ten_nhom) {
      throw new AppError('Tên nhóm khách hàng là bắt buộc', 400);
    }

    // Check if group code already exists
    if (ma_nhom) {
      const existingGroup = await NhomKhachHang.findOne({ where: { ma_nhom } });
      if (existingGroup) {
        throw new AppError('Mã nhóm khách hàng đã tồn tại', 400);
      }
    }

    // Create customer group
    const customerGroup = await NhomKhachHang.create({
      ten_nhom,
      mo_ta,
      ma_nhom,
      loai_nhom,
      ngay_tao: new Date()
    });

    res.status(201).json({
      success: true,
      message: 'Tạo nhóm khách hàng thành công!',
      data: {
        ...customerGroup.toJSON(),
        so_luong_khach_hang: 0
      }
    });
  } catch (error) {
    console.error('Error in createCustomerGroup:', error);
    throw new AppError(`Tạo nhóm người dùng thất bại: ${error.message}`, error.statusCode || 500);
  }
};
const updateCustomerGroup = async (req, res) => {
  try {
    const { id } = req.params;
    const { ten_nhom, mo_ta, ma_nhom, loai_nhom } = req.body;

    if (!ten_nhom) {
      throw new AppError('Tên nhóm khách hàng là bắt buộc', 400);
    }

    const customerGroup = await NhomKhachHang.findByPk(id);
    if (!customerGroup) {
      throw new AppError('Không tìm thấy nhóm khách hàng', 404);
    }

    // Check if group code already exists (excluding current group)
    if (ma_nhom && ma_nhom !== customerGroup.ma_nhom) {
      const existingGroup = await NhomKhachHang.findOne({
        where: { 
          ma_nhom,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingGroup) {
        throw new AppError('Mã nhóm khách hàng đã tồn tại', 400);
      }
    }

    await customerGroup.update({
      ten_nhom,
      mo_ta,
      ma_nhom,
      loai_nhom
    });

    // Get updated customer group with user count
    const userCount = await NhomKhachHangNguoiDung.count({
      where: { nhom_khach_hang_id: id }
    });

    res.json({
      success: true,
      message: 'Cập nhật nhóm khách hàng thành công',
      data: {
        ...customerGroup.toJSON(),
        so_luong_khach_hang: userCount
      }
    });
  } catch (error) {
    console.error('Error in updateCustomerGroup:', error);
    throw new AppError(`Cập nhật nhóm khách hàng thất bại: ${error.message}`, error.statusCode || 500);
  }
};

/**
 * Xóa nhóm khách hàng
 */
const deleteCustomerGroup = async (req, res) => {
  try {
    const { id } = req.params;

    // Tìm nhóm khách hàng
    const customerGroup = await NhomKhachHang.findByPk(id);
    if (!customerGroup) {
      throw new AppError('Không tìm thấy nhóm khách hàng', 404);
    }

    // Kiểm tra xem nhóm có đang được sử dụng không
    const usersInGroup = await NhomKhachHangNguoiDung.count({
      where: { nhom_khach_hang_id: id }
    });

    if (usersInGroup > 0) {
      throw new AppError('Không thể xóa nhóm khách hàng đang có người dùng', 400);
    }

    // Xóa nhóm khách hàng
    await customerGroup.destroy();

    res.json({
      success: true,
      message: 'Xóa nhóm khách hàng thành công'
    });
  } catch (error) {
    console.error('Error in deleteCustomerGroup:', error);
    throw new AppError(`Xóa nhóm khách hàng thất bại: ${error.message}`, error.statusCode || 500);
  }
};

module.exports = {
  getCustomerGroups,
  createCustomerGroup,
  updateCustomerGroup,
  deleteCustomerGroup,
  getAllCustomerGroupsNoPagin
};
