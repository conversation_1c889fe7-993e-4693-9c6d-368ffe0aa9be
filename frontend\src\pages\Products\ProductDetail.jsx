import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Tag,
  Button,
  Space,
  Image,
  Table,
  Spin,
  Alert,
  Divider,
  Row,
  Col
} from 'antd';
import { EditOutlined, ArrowLeftOutlined, EyeOutlined, HistoryOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useProduct, useProductStockHistory } from '../../hooks/useProducts';

const ProductDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const { data: product, isLoading, error } = useProduct(id);
  const { data: stockHistoryResponse, isLoading: historyLoading } = useProductStockHistory(id, { limit: 20 });

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>Đang tải thông tin sản phẩm...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Lỗi"
        description={error.message || 'Không thể tải thông tin sản phẩm'}
        type="error"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  if (!product) {
    return (
      <Alert
        message="Không tìm thấy sản phẩm"
        description="Sản phẩm không tồn tại hoặc đã bị xóa"
        type="warning"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  // Handle variant click
  const handleVariantClick = (variant) => {
    // Lưu thông tin vào localStorage để sử dụng ở trang chi tiết
    localStorage.setItem('selectedVariant', JSON.stringify(variant));
    localStorage.setItem('selectedProduct', JSON.stringify(product));

    // Navigate to variant detail page
    navigate(`/products/${id}/variants/${variant.id}`);
  };

  // Columns for variants table
  const variantColumns = [
    {
      title: 'Ảnh',
      dataIndex: 'anh_phien_ban',
      key: 'anh_phien_ban',
      width: 80,
      render: (anhPhienBan, record) => {
        // Sử dụng ảnh phiên bản nếu có, nếu không thì dùng ảnh đầu tiên của sản phẩm
        const imageUrl = anhPhienBan || (product?.anhList && product.anhList.length > 0 ? product.anhList[0].thumbnail_url || product.anhList[0].url : null);

        if (!imageUrl) {
          return (
            <div
              style={{
                width: 50,
                height: 50,
                backgroundColor: '#f5f5f5',
                borderRadius: 4,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999',
                fontSize: 12
              }}
            >
              No Image
            </div>
          );
        }

        return (
          <Image
            src={imageUrl}
            alt={record.ten_phien_ban}
            width={50}
            height={50}
            style={{ objectFit: 'cover', borderRadius: 4 }}
          />
        );
      }
    },
    {
      title: 'Tên phiên bản',
      dataIndex: 'ten_phien_ban',
      key: 'ten_phien_ban',
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => handleVariantClick(record)}
          style={{ padding: 0, height: 'auto' }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: 'Mã',
      dataIndex: 'ma',
      key: 'ma',
    },
    {
      title: 'Giá bán lẻ',
      dataIndex: 'gia_le',
      key: 'gia_le',
      render: (value) => value ? `${value.toLocaleString()} VNĐ` : '-',
    },
    {
      title: 'Giá bán buôn',
      dataIndex: 'gia_buon',
      key: 'gia_buon',
      render: (value) => value ? `${value.toLocaleString()} VNĐ` : '-',
    },
    {
      title: 'Giá nhập',
      dataIndex: 'gia_nhap',
      key: 'gia_nhap',
      render: (value) => value ? `${value.toLocaleString()} VNĐ` : '-',
    },
    {
      title: 'Đơn vị tính',
      dataIndex: 'don_vi_tinh',
      key: 'don_vi_tinh',
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="primary"
          size="small"
          icon={<InfoCircleOutlined />}
          onClick={() => handleVariantClick(record)}
        >
          Chi tiết
        </Button>
      ),
    },
  ];

  // Columns for stock history table
  const stockHistoryColumns = [
    {
      title: 'Ngày thực hiện',
      dataIndex: 'ngay_thuc_hien',
      key: 'ngay_thuc_hien',
      width: 150,
      render: (date) => new Date(date).toLocaleString('vi-VN'),
    },
    {
      title: 'Loại giao dịch',
      dataIndex: 'loai',
      key: 'loai',
      width: 120,
      render: (type) => {
        const typeMap = {
          'nhap': { color: 'green', text: 'Nhập kho' },
          'xuat': { color: 'red', text: 'Xuất kho' },
          'dieu_chinh': { color: 'blue', text: 'Điều chỉnh' },
          'chuyen_kho': { color: 'orange', text: 'Chuyển kho' }
        };
        const config = typeMap[type] || { color: 'default', text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: 'Phiên bản',
      dataIndex: 'phien_ban',
      key: 'phien_ban',
    },
    {
      title: 'Kho',
      dataIndex: 'ten_kho',
      key: 'ten_kho',
      width: 120,
    },
    {
      title: 'SL trước',
      dataIndex: 'so_luong_truoc',
      key: 'so_luong_truoc',
      width: 80,
      align: 'right',
      render: (value) => value?.toLocaleString() || '0',
    },
    {
      title: 'Thay đổi',
      dataIndex: 'so_luong_thay_doi',
      key: 'so_luong_thay_doi',
      width: 100,
      align: 'right',
      render: (value) => {
        const color = value > 0 ? 'green' : value < 0 ? 'red' : 'default';
        const prefix = value > 0 ? '+' : '';
        return <span style={{ color }}>{prefix}{value?.toLocaleString() || '0'}</span>;
      }
    },
    {
      title: 'SL sau',
      dataIndex: 'so_luong_sau',
      key: 'so_luong_sau',
      width: 80,
      align: 'right',
      render: (value) => value?.toLocaleString() || '0',
    },
    {
      title: 'Giá vốn',
      dataIndex: 'gia_von',
      key: 'gia_von',
      width: 120,
      align: 'right',
      render: (value) => value ? `${parseFloat(value).toLocaleString()} VNĐ` : '-',
    },
    {
      title: 'Lý do',
      dataIndex: 'ly_do',
      key: 'ly_do',
      ellipsis: true,
    },
    {
      title: 'Người thực hiện',
      dataIndex: 'nguoi_thuc_hien',
      key: 'nguoi_thuc_hien',
      width: 120,
    },
  ];

  // Extract stock history data
  const stockHistory = stockHistoryResponse?.data || [];

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/products')}
          >
            Quay lại
          </Button>
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={() => navigate(`/products/edit/${id}`)}
          >
            Chỉnh sửa
          </Button>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        {/* Product Images */}
        <Col xs={24} md={8}>
          <Card title="Ảnh sản phẩm" size="small">
            {product.anhList && product.anhList.length > 0 ? (
              <div>
                <Image.PreviewGroup>
                  {product.anhList.map((image, index) => (
                    <Image
                      key={index}
                      src={image.thumbnail_url || image.url}
                      alt={`${product.ten} - ${index + 1}`}
                      style={{ 
                        width: '100%', 
                        marginBottom: 8,
                        borderRadius: 8
                      }}
                    />
                  ))}
                </Image.PreviewGroup>
              </div>
            ) : (
              <div style={{ 
                textAlign: 'center', 
                padding: '40px 20px',
                color: '#999'
              }}>
                <EyeOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <p>Chưa có ảnh sản phẩm</p>
              </div>
            )}
          </Card>
        </Col>

        {/* Product Info */}
        <Col xs={24} md={16}>
          <Card title="Thông tin sản phẩm" size="small">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Tên sản phẩm" span={2}>
                <strong>{product.ten}</strong>
              </Descriptions.Item>
              
              <Descriptions.Item label="Mã sản phẩm">
                {product.ma || '-'}
              </Descriptions.Item>
              
              <Descriptions.Item label="Trạng thái">
                <Tag color={product.trang_thai === 'active' ? 'green' : 'red'}>
                  {product.trang_thai === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                </Tag>
              </Descriptions.Item>
              
              <Descriptions.Item label="Loại sản phẩm">
                {product.loaiSanPham?.ten || '-'}
              </Descriptions.Item>
              
              <Descriptions.Item label="Nhãn hiệu">
                {product.nhanHieu?.ten || '-'}
              </Descriptions.Item>
              
              <Descriptions.Item label="Tag">
                {product.tag?.ten || '-'}
              </Descriptions.Item>
              
              <Descriptions.Item label="Số phiên bản">
                <Tag color="blue">{product.phienBanList?.length || 0} phiên bản</Tag>
              </Descriptions.Item>
              
              <Descriptions.Item label="Mô tả" span={2}>
                {product.mo_ta || '-'}
              </Descriptions.Item>
              
              <Descriptions.Item label="Người tạo">
                {product.nguoi_tao || '-'}
              </Descriptions.Item>
              
              <Descriptions.Item label="Ngày tạo">
                {product.ngay_tao ? new Date(product.ngay_tao).toLocaleString('vi-VN') : '-'}
              </Descriptions.Item>
              
              <Descriptions.Item label="Người cập nhật">
                {product.nguoi_cap_nhap || '-'}
              </Descriptions.Item>
              
              <Descriptions.Item label="Ngày cập nhật">
                {product.ngay_cap_nhap ? new Date(product.ngay_cap_nhap).toLocaleString('vi-VN') : '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>

      {/* Product Variants */}
      {product.phienBanList && product.phienBanList.length > 0 && (
        <>
          <Divider />
          <Card title="Danh sách phiên bản" size="small">
            <Table
              columns={variantColumns}
              dataSource={product.phienBanList}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </>
      )}

      {/* Stock History */}
      <Divider />
      <Card
        title={
          <Space>
            <HistoryOutlined />
            <span>Lịch sử xuất nhập kho</span>
          </Space>
        }
        size="small"
      >
        <Table
          columns={stockHistoryColumns}
          dataSource={stockHistory}
          rowKey="id"
          loading={historyLoading}
          pagination={{
            total: stockHistoryResponse?.pagination?.total || 0,
            pageSize: 20,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} giao dịch`,
          }}
          size="small"
          scroll={{ x: 1200 }}
          locale={{
            emptyText: stockHistory.length === 0 && !historyLoading ?
              'Chưa có lịch sử xuất nhập kho' : undefined
          }}
        />
      </Card>


    </div>
  );
};

export default ProductDetail;
