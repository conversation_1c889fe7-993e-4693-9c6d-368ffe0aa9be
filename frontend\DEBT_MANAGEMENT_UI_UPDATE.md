# 🎨 Cập Nhật UI Trang Quản Lý Công Nợ

## ✅ **Đã cập nhật để hiển thị đúng với API mới:**

### **1. Statistics Cards - Hiển thị 2 loại công nợ:**

**Trước (1 loại):**
```
[Tổng công nợ] [Nợ quá hạn] [Số KH có nợ] [KH quá hạn]
```

**Sau (2 loại):**
```
[Công nợ hiện tại] [Công nợ thực tế] [Nợ quá hạn] [KH có nợ thực tế]
    (Dự tính)        (Đã giao)
```

### **2. Table Columns - Thêm cột công nợ thực tế:**

**Trước:**
```
| Khách hàng | Tổng nợ | Nợ quá hạn | Trạng thái | Hành động |
```

**Sau:**
```
| Khách hàng | Công nợ hiện tại | Công nợ thực tế | Nợ quá hạn | Trạng thái | Hành động |
|            |    (Dự tính)     |   (Đã giao)     |            |            |            |
```

### **3. Payment Modal - Hiển thị cả 2 loại công nợ:**

**Trước:**
```
Số tiền thu (Tổng nợ: 500,000đ)
```

**Sau:**
```
Số tiền thu
Công nợ hiện tại: 500,000đ | Thực tế: 200,000đ
```

## 🔧 **Chi tiết thay đổi:**

### **File: SaleSysFE/src/pages/Accounting/DebtManagement.jsx**

#### **1. Statistics Cards (Line 598-667):**
```jsx
// Card 1: Công nợ hiện tại
<Statistic
  title="Công nợ hiện tại"
  value={stats.total_debt || 0}
  valueStyle={{ color: "#ff4d4f" }}
  suffix={<div style={{ fontSize: "12px", color: "#666" }}>(Dự tính)</div>}
/>

// Card 2: Công nợ thực tế  
<Statistic
  title="Công nợ thực tế"
  value={stats.actual_debt || 0}
  valueStyle={{ color: "#fa8c16" }}
  suffix={<div style={{ fontSize: "12px", color: "#666" }}>(Đã giao)</div>}
/>

// Card 4: KH có nợ thực tế
<Statistic
  title="KH có nợ thực tế"
  value={stats.customers_with_actual_debt || 0}
  valueStyle={{ color: "#1890ff" }}
/>
```

#### **2. Table Column - Công nợ hiện tại (Line 198-231):**
```jsx
{
  title: "Công nợ hiện tại",
  dataIndex: "total_debt",
  render: (value) => (
    <div style={{ textAlign: "right" }}>
      <Text strong style={{ color }}>
        {formattedValue}
      </Text>
      <div style={{ fontSize: "11px", color: "#666" }}>(Dự tính)</div>
    </div>
  ),
}
```

#### **3. Table Column - Công nợ thực tế (Line 232-260):**
```jsx
{
  title: "Công nợ thực tế",
  dataIndex: "actual_debt",
  render: (value) => (
    <div style={{ textAlign: "right" }}>
      <Text strong style={{ color: isZero ? "#52c41a" : "#fa8c16" }}>
        {new Intl.NumberFormat("vi-VN", {
          style: "currency",
          currency: "VND",
        }).format(value || 0)}
      </Text>
      <div style={{ fontSize: "11px", color: "#666" }}>(Đã giao)</div>
    </div>
  ),
}
```

#### **4. Payment Modal Label (Line 874-909):**
```jsx
label={(() => {
  const currentDebt = paymentModal.customer?.total_debt || 0;
  const actualDebt = paymentModal.customer?.actual_debt || 0;
  
  if (currentDebt > 0) {
    return (
      <div>
        Số tiền thu
        <div style={{ fontSize: "12px", color: "#666" }}>
          Công nợ hiện tại: {formattedCurrent} | Thực tế: {formattedActual}
        </div>
      </div>
    );
  }
  // ...
})()}
```

## 🎨 **Màu sắc và Style:**

### **Color Scheme:**
- **Công nợ hiện tại:** `#ff4d4f` (đỏ) - Dự tính, cần theo dõi
- **Công nợ thực tế:** `#fa8c16` (cam) - Thực tế, cần thu hồi
- **Nợ quá hạn:** `#ff7875` (đỏ nhạt) - Khẩn cấp
- **Không nợ:** `#52c41a` (xanh) - An toàn

### **Typography:**
- **Main value:** `14px`, `bold`
- **Sub label:** `11px`, `#666` (gray)
- **Modal sub info:** `12px`, `#666`

## 📊 **Ví dụ hiển thị:**

### **Statistics Cards:**
```
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│ Công nợ hiện tại│ │ Công nợ thực tế │ │   Nợ quá hạn   │ │ KH có nợ thực tế│
│   2,500,000đ    │ │   1,200,000đ    │ │    800,000đ    │ │       15        │
│   (Dự tính)     │ │   (Đã giao)     │ │                │ │                 │
└─────────────────┘ └─────────────────┘ └─────────────────┘ └─────────────────┘
```

### **Table Row:**
```
| Nguyễn Văn A | 500,000đ  | 200,000đ  | 100,000đ | Có nợ | [Thu tiền] [Chi tiết] |
|              | (Dự tính) | (Đã giao) |          |       |                       |
```

### **Payment Modal:**
```
┌─────────────────────────────────────────────────────────┐
│ Số tiền thu                                             │
│ Công nợ hiện tại: 500,000đ | Thực tế: 200,000đ         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [        300,000        ] VND                       │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 🎯 **Lợi ích UI mới:**

### **1. Thông tin rõ ràng:**
- Phân biệt rõ công nợ dự tính vs thực tế
- Màu sắc trực quan, dễ nhận biết
- Sub-label giải thích ý nghĩa

### **2. Workflow tốt hơn:**
- Manager thấy được công nợ dự tính (planning)
- Debt collector thấy công nợ thực tế (action)
- Payment modal hiển thị đầy đủ context

### **3. Consistent Design:**
- Color scheme nhất quán
- Typography hierarchy rõ ràng
- Responsive layout

## 🧪 **Test UI:**

### **Test Case 1: Khách hàng có cả 2 loại nợ**
```
Dữ liệu: total_debt = 500,000đ, actual_debt = 200,000đ
Hiển thị:
- Statistics: 2 cards khác nhau
- Table: 2 cột khác nhau  
- Modal: Hiển thị cả 2 số
```

### **Test Case 2: Khách hàng chỉ có nợ dự tính**
```
Dữ liệu: total_debt = 300,000đ, actual_debt = 0đ
Hiển thị:
- Công nợ hiện tại: 300,000đ (đỏ)
- Công nợ thực tế: 0đ (xanh)
```

### **Test Case 3: Khách hàng không có nợ**
```
Dữ liệu: total_debt = 0đ, actual_debt = 0đ
Hiển thị:
- Cả 2 cột đều: 0đ (xanh)
- Modal: "Không có công nợ"
```

## 🚀 **Hoàn thành:**

Trang Debt Management đã được cập nhật hoàn toàn để:
- ✅ **Hiển thị 2 loại công nợ** rõ ràng
- ✅ **Statistics cards** phân biệt dự tính vs thực tế
- ✅ **Table columns** đầy đủ thông tin
- ✅ **Payment modal** context đầy đủ
- ✅ **Color scheme** trực quan
- ✅ **Responsive design** tốt

Bây giờ UI sẽ hiển thị chính xác theo API mới với 2 loại công nợ! 🎨
