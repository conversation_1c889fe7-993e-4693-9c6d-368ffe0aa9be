'use strict';

const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
require('dotenv').config(); // <PERSON><PERSON><PERSON> bảo load biến môi trường từ .env

const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
const config = require(__dirname + '/../config/database.js')[env]; // Bạn đặt tên là database.js
const db = {};

let sequelize;

// ✅ Không dùng DATABASE_URL nữa
sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  config
);

// 🔍 Tự động nạp các model trong thư mục
fs
  .readdirSync(__dirname)
  .filter(file => {
    return (
      file.indexOf('.') !== 0 &&
      file !== basename &&
      file.slice(-3) === '.js' &&
      file.indexOf('.test.js') === -1
    );
  })
  .forEach(file => {
    const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
    db[model.name] = model;
  });

// 🔗 Ánh xạ liên kết giữa các model nếu có
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

// 📦 Export Sequelize và models
db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db;
