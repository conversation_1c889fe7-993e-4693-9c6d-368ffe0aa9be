import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  Divider,
  Alert,
  Row,
  Col
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons';

import { useAuth } from '../../contexts/AuthContext';

const { Title, Text } = Typography;

const Login = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { login, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  const handleSubmit = async (values) => {
    setLoading(true);
    setError('');

    try {
      const result = await login(values);
      
      if (result.success) {
        const from = location.state?.from?.pathname || '/dashboard';
        navigate(from, { replace: true });
      } else {
        setError(result.message || 'Đăng nhập thất bại');
      }
    } catch (err) {
      setError('Có lỗi xảy ra khi đăng nhập');
    } finally {
      setLoading(false);
    }
  };

  const demoAccounts = [
    {
      title: 'Quản trị viên',
      email: '<EMAIL>',
      password: '123456',
      description: 'Có tất cả quyền trong hệ thống'
    },
    {
      title: 'Quản lý',
      email: '<EMAIL>',
      password: '123456',
      description: 'Quản lý các hoạt động kinh doanh'
    },
    {
      title: 'Nhân viên bán hàng',
      email: '<EMAIL>',
      password: '123456',
      description: 'Thực hiện các giao dịch bán hàng'
    },
    {
      title: 'Nhân viên kho',
      email: '<EMAIL>',
      password: '123456',
      description: 'Quản lý kho hàng và tồn kho'
    },
    {
      title: 'Kế toán',
      email: '<EMAIL>',
      password: '123456',
      description: 'Quản lý tài chính và báo cáo'
    }
  ];

  const fillDemoAccount = (email, password) => {
    form.setFieldsValue({ email, mat_khau: password });
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Row gutter={[32, 32]} style={{ width: '100%', maxWidth: 1200 }}>
        {/* Login Form */}
        <Col xs={24} lg={12}>
          <Card
            style={{
              maxWidth: 400,
              margin: '0 auto',
              borderRadius: 12,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
          >
            <div style={{ textAlign: 'center', marginBottom: 32 }}>
              <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
                SAPO Clone
              </Title>
              <Text type="secondary">
                Phần mềm quản lý bán hàng
              </Text>
            </div>

            {error && (
              <Alert
                message={error}
                type="error"
                showIcon
                style={{ marginBottom: 24 }}
              />
            )}

            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              layout="vertical"
              size="large"
            >
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Vui lòng nhập email!' },
                  { type: 'email', message: 'Email không hợp lệ!' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="Nhập email của bạn"
                />
              </Form.Item>

              <Form.Item
                name="mat_khau"
                label="Mật khẩu"
                rules={[
                  { required: true, message: 'Vui lòng nhập mật khẩu!' },
                  { min: 6, message: 'Mật khẩu phải có ít nhất 6 ký tự!' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="Nhập mật khẩu của bạn"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  style={{ height: 48 }}
                >
                  Đăng nhập
                </Button>
              </Form.Item>
            </Form>

            <Divider>Hoặc</Divider>

            <div style={{ textAlign: 'center' }}>
              <Text type="secondary">
                Chưa có tài khoản? Sử dụng tài khoản demo bên cạnh
              </Text>
            </div>
          </Card>
        </Col>

        {/* Demo Accounts */}
        <Col xs={24} lg={12}>
          <Card
            title="Tài khoản demo"
            style={{
              borderRadius: 12,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
          >
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <Text type="secondary">
                Nhấp vào tài khoản bên dưới để tự động điền thông tin đăng nhập:
              </Text>
              
              {demoAccounts.map((account, index) => (
                <Card
                  key={index}
                  size="small"
                  hoverable
                  onClick={() => fillDemoAccount(account.email, account.password)}
                  style={{
                    cursor: 'pointer',
                    border: '1px solid #d9d9d9',
                    borderRadius: 8
                  }}
                >
                  <div>
                    <Text strong style={{ color: '#1890ff' }}>
                      {account.title}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {account.email}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {account.description}
                    </Text>
                  </div>
                </Card>
              ))}

              <Alert
                message="Thông tin đăng nhập"
                description="Tất cả tài khoản demo đều có mật khẩu: 123456"
                type="info"
                showIcon
              />
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Login;
