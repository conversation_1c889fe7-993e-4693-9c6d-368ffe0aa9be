'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class LichSuKho extends Model {
    static associate(models) {
      // Quan hệ với phiên bản sản phẩm
      LichSuKho.belongsTo(models.PhienBanSanPham, {
        foreignKey: 'phien_ban_san_pham_id',
        as: 'phienBanSanPham'
      });

      // Quan hệ với kho hàng (kho nguồn)
      LichSuKho.belongsTo(models.KhoHang, {
        foreignKey: 'kho_hang_id',
        as: 'khoHang'
      });

      // Quan hệ với kho chuyển đến (cho trường hợp chuyển kho)
      LichSuKho.belongsTo(models.KhoHang, {
        foreignKey: 'kho_chuyen_den_id',
        as: 'khoChuyenDen'
      });
    }
  }

  LichSuKho.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    phien_ban_san_pham_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'phien_ban_san_pham',
        key: 'id'
      }
    },
    kho_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'kho_hang',
        key: 'id'
      }
    },
    loai_giao_dich: {
      type: DataTypes.ENUM('nhap', 'xuat', 'chuyen', 'dieu_chinh', 'kiem_ke'),
      allowNull: false,
      comment: 'Loại giao dịch: nhap, xuat, chuyen, dieu_chinh, kiem_ke'
    },
    so_luong_truoc: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Số lượng trước khi thay đổi'
    },
    so_luong_thay_doi: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Số lượng thay đổi (có thể âm)'
    },
    so_luong_sau: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Số lượng sau khi thay đổi'
    },
    gia_von: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      comment: 'Giá vốn tại thời điểm giao dịch'
    },
    ly_do: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Lý do thay đổi'
    },
    don_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'ID đơn hàng (nếu có)'
    },
    kho_chuyen_den_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'kho_hang',
        key: 'id'
      },
      comment: 'ID kho chuyển đến (nếu là chuyển kho)'
    },
    nguoi_thuc_hien: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'Người thực hiện giao dịch'
    },
    ghi_chu: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Ghi chú thêm'
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ngay_thuc_hien: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Thời gian thực hiện giao dịch'
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Ngày tạo bản ghi'
    },
    ngay_cap_nhap: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Ngày cập nhật bản ghi'
    }
  }, {
    sequelize,
    modelName: 'LichSuKho',
    tableName: 'lich_su_kho',
    timestamps: false // Sử dụng custom timestamp fields
  });

  return LichSuKho;
};
