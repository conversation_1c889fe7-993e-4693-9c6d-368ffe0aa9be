'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Quyen extends Model {
    static associate(models) {
      // Quan hệ với vai trò (n-n)
      Quyen.belongsToMany(models.VaiTro, {
        through: models.VaiTroQuyen,
        foreignKey: 'quyen_id',
        otherKey: 'vai_tro_id',
        as: 'vaiTroList'
      });
    }
  }

  Quyen.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ma_quyen: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        isUppercase: true
      }
    },
    ten_quyen: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    mo_ta: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Quyen',
    tableName: 'quyen',
    timestamps: false
  });

  return Quyen;
};
