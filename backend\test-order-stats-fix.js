require('dotenv').config();
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sequelize } = require('./models');

async function testOrderStatsFix() {
  try {
    console.log('🧪 Testing Order Statistics Fix...\n');

    // 1. Tạo khách hàng test
    console.log('1️⃣ Creating test customer...');
    const customer = await NguoiDung.create({
      ho_ten: 'Nguyễn Văn Test Stats',
      so_dien_thoai: '0987654222',
      email: '<EMAIL>',
      loai_nguoi_dung: 'khach_hang',
      trang_thai: 'dang_giao_dich'
    });
    console.log(`✅ Created customer: ${customer.ho_ten} (ID: ${customer.id})`);

    // 2. Tạo đơn hàng test với các trạng thái khác nhau
    console.log('\n2️⃣ Creating test orders...');
    
    const orders = [
      {
        ma_don_hang: `STATS-1-${Date.now()}`,
        trang_thai: 'cho_xu_ly', // Chờ xử lý
        tong_tien: 1000000, // 1M
        tong_phai_tra: 500000, // 500K COD
        desc: 'Pending order'
      },
      {
        ma_don_hang: `STATS-2-${Date.now() + 1}`,
        trang_thai: 'da_xac_nhan', // Đã xác nhận
        tong_tien: 800000, // 800K
        tong_phai_tra: 400000, // 400K COD
        desc: 'Confirmed order'
      },
      {
        ma_don_hang: `STATS-3-${Date.now() + 2}`,
        trang_thai: 'hoan_thanh', // Hoàn thành
        tong_tien: 1500000, // 1.5M
        tong_phai_tra: 750000, // 750K COD
        desc: 'Completed order 1'
      },
      {
        ma_don_hang: `STATS-4-${Date.now() + 3}`,
        trang_thai: 'hoan_thanh', // Hoàn thành
        tong_tien: 2000000, // 2M
        tong_phai_tra: 1000000, // 1M COD
        desc: 'Completed order 2'
      },
      {
        ma_don_hang: `STATS-5-${Date.now() + 4}`,
        trang_thai: 'huy', // Hủy
        tong_tien: 600000, // 600K
        tong_phai_tra: 300000, // 300K COD
        desc: 'Cancelled order'
      }
    ];

    const createdOrders = [];
    for (const orderData of orders) {
      const order = await DonHang.create({
        ma_don_hang: orderData.ma_don_hang,
        khach_hang_id: customer.id,
        ngay_ban: new Date(),
        trang_thai: orderData.trang_thai,
        tong_tien: orderData.tong_tien,
        tong_phai_tra: orderData.tong_phai_tra,
        tong_da_tra: 0,
        con_phai_tra: orderData.tong_tien - orderData.tong_phai_tra,
        tien_cod: orderData.tong_phai_tra,
        tien_coc: 0,
        ghi_chu: `Test ${orderData.desc}`
      });
      
      createdOrders.push(order);
      console.log(`   ✅ ${orderData.desc}: ${orderData.ma_don_hang} (${orderData.tong_tien.toLocaleString('vi-VN')}đ)`);
    }

    // 3. Test calculateOrderStats function
    console.log('\n3️⃣ Testing calculateOrderStats function...');
    
    // Simulate calculateOrderStats logic
    const [totalOrders, pendingOrders, completedOrders, totalRevenue] = await Promise.all([
      // Tổng số đơn hàng
      DonHang.count(),
      
      // Đơn hàng chờ xác nhận
      DonHang.count({ where: { trang_thai: "cho_xu_ly" } }),
      
      // Đơn hàng đã hoàn thành
      DonHang.count({ where: { trang_thai: "hoan_thanh" } }),
      
      // Tổng doanh thu từ đơn hàng hoàn thành (tính theo tong_tien)
      DonHang.sum("tong_tien", {
        where: {
          trang_thai: "hoan_thanh",
        },
      }),
    ]);

    const stats = {
      total_orders: totalOrders || 0,
      pending_orders: pendingOrders || 0,
      completed_orders: completedOrders || 0,
      total_revenue: totalRevenue || 0,
    };

    console.log('📊 Calculated Statistics:');
    console.log(`   - Total Orders: ${stats.total_orders}`);
    console.log(`   - Pending Orders: ${stats.pending_orders}`);
    console.log(`   - Completed Orders: ${stats.completed_orders}`);
    console.log(`   - Total Revenue: ${stats.total_revenue.toLocaleString('vi-VN')}đ`);

    // 4. Verify expected results
    console.log('\n4️⃣ Verifying expected results...');
    
    const expectedStats = {
      total_orders: 5, // Tất cả đơn hàng (bao gồm cả đơn cũ nếu có)
      pending_orders: 1, // 1 đơn chờ xử lý
      completed_orders: 2, // 2 đơn hoàn thành
      total_revenue: 1500000 + 2000000, // 3.5M từ 2 đơn hoàn thành
    };

    console.log('🎯 Expected vs Actual:');
    console.log(`   - Pending Orders: Expected >= ${expectedStats.pending_orders}, Got: ${stats.pending_orders}`);
    console.log(`   - Completed Orders: Expected >= ${expectedStats.completed_orders}, Got: ${stats.completed_orders}`);
    console.log(`   - Revenue: Expected >= ${expectedStats.total_revenue.toLocaleString('vi-VN')}đ, Got: ${stats.total_revenue.toLocaleString('vi-VN')}đ`);

    // 5. Test revenue calculation logic
    console.log('\n5️⃣ Testing revenue calculation logic...');
    
    console.log('📋 Revenue Calculation:');
    console.log('   - OLD Logic: SUM(tong_phai_tra) WHERE trang_thai = "hoan_thanh"');
    console.log('   - NEW Logic: SUM(tong_tien) WHERE trang_thai = "hoan_thanh"');
    
    // Calculate old vs new logic
    const oldRevenue = await DonHang.sum("tong_phai_tra", {
      where: { trang_thai: "hoan_thanh" }
    });
    
    const newRevenue = await DonHang.sum("tong_tien", {
      where: { trang_thai: "hoan_thanh" }
    });

    console.log(`   - Old Revenue (tong_phai_tra): ${(oldRevenue || 0).toLocaleString('vi-VN')}đ`);
    console.log(`   - New Revenue (tong_tien): ${(newRevenue || 0).toLocaleString('vi-VN')}đ`);
    console.log(`   - Difference: ${((newRevenue || 0) - (oldRevenue || 0)).toLocaleString('vi-VN')}đ`);

    // 6. Test frontend display
    console.log('\n6️⃣ Testing frontend display...');
    
    console.log('🖥️ Frontend Statistics Cards:');
    console.log(`   📊 Tổng đơn hàng: ${stats.total_orders}`);
    console.log(`   ⏳ Chờ xác nhận: ${stats.pending_orders} (màu vàng)`);
    console.log(`   ✅ Đã hoàn thành: ${stats.completed_orders} (màu xanh)`);
    console.log(`   💰 Doanh thu: ${stats.total_revenue.toLocaleString('vi-VN')}đ (màu xanh dương)`);

    // 7. Cleanup
    console.log('\n7️⃣ Cleaning up...');
    await DonHang.destroy({ where: { khach_hang_id: customer.id } });
    await customer.destroy();
    console.log('✅ Cleanup completed');

    // 8. Final verification
    console.log('\n8️⃣ Final Verification:');
    console.log('🎯 Statistics Logic Summary:');
    console.log('   ✅ total_orders: COUNT(*) - Tất cả đơn hàng');
    console.log('   ✅ pending_orders: COUNT(*) WHERE trang_thai = "cho_xu_ly"');
    console.log('   ✅ completed_orders: COUNT(*) WHERE trang_thai = "hoan_thanh"');
    console.log('   ✅ total_revenue: SUM(tong_tien) WHERE trang_thai = "hoan_thanh"');
    
    console.log('\n📊 Revenue Calculation:');
    console.log('   - tong_tien: Giá trị thực tế của sản phẩm trong đơn hàng');
    console.log('   - tong_phai_tra: Số tiền khách hàng cần thanh toán (COD)');
    console.log('   - Doanh thu = Tổng giá trị sản phẩm đã bán (tong_tien)');
    
    console.log('\n🎉 Order statistics fix completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy test
testOrderStatsFix();
