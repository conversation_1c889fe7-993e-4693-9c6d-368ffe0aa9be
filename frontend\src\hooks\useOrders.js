import { useQuery, useMutation, useQueryClient } from "react-query";
import { message } from "antd";
import { ordersAPI, testAPI } from "../services/api";

// Query keys
const ORDER_QUERY_KEYS = {
  orders: () => ["orders"],
  order: (id) => ["orders", id],
  orderDetails: (id) => ["orders", id, "details"],
};

// Get orders list
export const useOrders = (params = {}) => {
  return useQuery(
    [...ORDER_QUERY_KEYS.orders(), params],
    () => {
      return testAPI.getOrders(params);
    },
    {
      select: (response) => {
        // API test trả về trực tiếp data với field mapping mới
        if (response?.success && response?.data) {
          return {
            ...response,
            data: response.data.map((order) => ({
              ...order,
              // Map field names for compatibility with frontend
              ngay_dat_hang: order.ngay_ban,
              tong_tien_hang: order.tong_tien,
              giam_gia: order.chiet_khau,
              tong_thanh_toan: order.tong_phai_tra,
            })),
            // Map stats field names
            stats: response.stats
              ? {
                  total_orders: response.stats.total_orders,
                  pending_orders: response.stats.pending_orders,
                  completed_orders: response.stats.completed_orders,
                  total_revenue: response.stats.total_revenue,
                }
              : {},
          };
        }
        return response;
      },
      staleTime: 30 * 1000, // 30 seconds
    }
  );
};

// Get single order
export const useOrder = (id) => {
  return useQuery(ORDER_QUERY_KEYS.order(id), () => ordersAPI.getOrder(id), {
    enabled: !!id,
    select: (data) => data.data,
    staleTime: 30 * 1000,
  });
};

// Get order details
export const useOrderDetails = (id) => {
  return useQuery(
    ORDER_QUERY_KEYS.orderDetails(id),
    () => ordersAPI.getOrderDetails(id),
    {
      enabled: !!id,
      select: (data) => data.data,
      staleTime: 30 * 1000,
    }
  );
};

// Create order mutation
export const useCreateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (orderData) => {
      console.log("📋 Sending order data to API:", orderData);
      return testAPI.createOrder(orderData);
    },
    {
      onSuccess: (data) => {
        console.log("📋 Order creation success:", data);
        if (data?.success) {
          message.success(
            `Tạo đơn hàng thành công! Mã đơn: ${data.data?.ma_don_hang}`
          );
        } else {
          message.success("Tạo đơn hàng thành công!");
        }
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
        // Invalidate inventory to update stock
        queryClient.invalidateQueries(["inventory"]);
        queryClient.invalidateQueries(["product-variants"]);
      },
      onError: (error) => {
        console.error("📋 Order creation error:", error);
        message.error(error.message || "Có lỗi xảy ra khi tạo đơn hàng");
      },
    }
  );
};

// Update order mutation
export const useUpdateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(({ id, ...data }) => ordersAPI.updateOrder(id, data), {
    onSuccess: (data, variables) => {
      message.success("Cập nhật đơn hàng thành công!");
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.order(variables.id));
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
    },
    onError: (error) => {
      message.error(
        error.response?.data?.message || "Có lỗi xảy ra khi cập nhật đơn hàng"
      );
    },
  });
};

// Delete order mutation
export const useDeleteOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(ordersAPI.deleteOrder, {
    onSuccess: () => {
      message.success("Xóa đơn hàng thành công!");
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
    },
    onError: (error) => {
      message.error(
        error.response?.data?.message || "Có lỗi xảy ra khi xóa đơn hàng"
      );
    },
  });
};

// Update order status mutation
export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, status }) => {
      console.log("🔄 Updating order status:", { id, status });
      return testAPI.updateOrderStatus(id, status);
    },
    {
      onSuccess: (data, variables) => {
        console.log("✅ Order status updated successfully:", data);
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.order(variables.id));
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
        // Invalidate inventory if order is completed/cancelled/returned
        if (["hoan_thanh", "huy", "hoan_hang"].includes(variables.status)) {
          queryClient.invalidateQueries(["inventory"]);
        }
      },
      onError: (error) => {
        console.error("❌ Error updating order status:", error);
        message.error(error.message || "Có lỗi xảy ra khi cập nhật trạng thái");
      },
    }
  );
};

// Hook để hoàn hàng
export const useReturnOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, returnData }) => {
      console.log("🔄 Processing order return:", { id, returnData });
      return testAPI.returnOrder(id, returnData);
    },
    {
      onSuccess: (data, variables) => {
        console.log("✅ Order returned successfully:", data);
        message.success("Hoàn hàng thành công!");
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.order(variables.id));
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
        // Invalidate inventory and debt queries
        queryClient.invalidateQueries(["inventory"]);
        queryClient.invalidateQueries(["debt"]);
      },
      onError: (error) => {
        console.error("❌ Error returning order:", error);
        message.error(error.message || "Có lỗi xảy ra khi hoàn hàng");
      },
    }
  );
};

// Cancel order mutation
export const useCancelOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(ordersAPI.cancelOrder, {
    onSuccess: (data, id) => {
      message.success("Hủy đơn hàng thành công!");
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.order(id));
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
      // Invalidate inventory to restore stock
      queryClient.invalidateQueries(["inventory"]);
    },
    onError: (error) => {
      message.error(
        error.response?.data?.message || "Có lỗi xảy ra khi hủy đơn hàng"
      );
    },
  });
};

// Complete order mutation
export const useCompleteOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(ordersAPI.completeOrder, {
    onSuccess: (data, id) => {
      message.success("Hoàn thành đơn hàng thành công!");
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.order(id));
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
      // Invalidate inventory to update stock
      queryClient.invalidateQueries(["inventory"]);
    },
    onError: (error) => {
      message.error(
        error.response?.data?.message || "Có lỗi xảy ra khi hoàn thành đơn hàng"
      );
    },
  });
};

// Create payment for order mutation
export const useCreateOrderPayment = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (paymentData) => {
      console.log("💰 Creating payment for order:", paymentData);
      return testAPI.createPayment(paymentData);
    },
    {
      onSuccess: (data, variables) => {
        console.log("✅ Payment created successfully:", data);
        message.success("Thanh toán thành công!");

        // Invalidate related queries
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
        if (variables.order_id) {
          queryClient.invalidateQueries(
            ORDER_QUERY_KEYS.order(variables.order_id)
          );
        }
        // Invalidate debt queries
        queryClient.invalidateQueries(["debt"]);
        queryClient.invalidateQueries(["debt-customer", variables.customer_id]);
      },
      onError: (error) => {
        console.error("❌ Error creating payment:", error);
        message.error(error.message || "Có lỗi xảy ra khi thanh toán");
      },
    }
  );
};

export { ORDER_QUERY_KEYS };
