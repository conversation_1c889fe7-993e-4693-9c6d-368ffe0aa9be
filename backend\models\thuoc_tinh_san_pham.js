'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class ThuocTinhSanPham extends Model {
    static associate(models) {
      // Quan hệ với sản phẩm
      ThuocTinhSanPham.belongsTo(models.SanPham, {
        foreignKey: 'san_pham_id',
        as: 'sanPham'
      });

      // Quan hệ với thuộc tính
      ThuocTinhSanPham.belongsTo(models.ThuocTinh, {
        foreignKey: 'thuoc_tinh_id',
        as: 'thuocTinh'
      });
    }
  }

  ThuocTinhSanPham.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    san_pham_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'san_pham',
        key: 'id'
      }
    },
    thuoc_tinh_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'thuoc_tinh',
        key: 'id'
      }
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'ThuocTinhSanPham',
    tableName: 'thuoc_tinh_san_pham',
    indexes: [
      {
        unique: true,
        fields: ['san_pham_id', 'thuoc_tinh_id'],
        name: 'sp_thuoc_tinh_unique'
      }
    ]
  });

  return ThuocTinhSanPham;
};
