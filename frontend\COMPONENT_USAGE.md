# Hướng dẫn sử dụng Common Components

## 🎯 Tổng quan

Đã tạo thành công các component chung để tạo giao diện giống như trong ảnh bạn cung cấp:

### ✅ **DataTable Component**
- **Tính năng**: Bảng dữ liệu với header, search, filter, actions
- **Giao diện**: Giống như trong ảnh với title màu xanh, search box, filter dropdown
- **Actions**: Edit, Delete, View với icons

### ✅ **PageHeader Component** 
- **Tính năng**: Header trang với breadcrumb, title, statistics
- **Layout**: Responsive với actions và thống kê

### ✅ **SimpleSearchFilter Component**
- **Tính năng**: Search và filter đơn giản
- **Layout**: Responsive grid layout

## 🚀 Cách sử dụng

### **1. Import Components**
```jsx
import { DataTable, PageHeader } from '../../components/Common';
```

### **2. Sử dụng DataTable (Giống như trong ảnh)**
```jsx
<DataTable
  title="Tất cả sản phẩm"           // Title màu xanh
  data={products}
  columns={columns}
  loading={loading}
  
  // Search và Filter (như trong ảnh)
  showSearch={true}
  searchPlaceholder="Tìm kiếm sản phẩm theo tên, mã..."
  filters={[
    {
      key: 'category',
      placeholder: 'Loại sản phẩm',
      options: [
        { value: 'ao', label: 'Áo thun' },
        { value: 'quan', label: 'Quần jean' }
      ]
    },
    {
      key: 'status',
      placeholder: 'Trạng thái',
      options: [
        { value: 'active', label: 'Hoạt động' },
        { value: 'inactive', label: 'Ngừng bán' }
      ]
    }
  ]}
  
  // Nút thêm mới (như trong ảnh)
  showAddButton={true}
  onAdd={handleCreate}
  addButtonText="Thêm sản phẩm"
  
  // Actions (Edit, Delete, View)
  onEdit={handleEdit}
  onDelete={handleDelete}
  onView={handleView}
  
  // Pagination
  pagination={{
    current: 1,
    pageSize: 10,
    total: 100,
    showSizeChanger: true,
    showQuickJumper: true
  }}
  
  rowKey="id"
/>
```

### **3. Cấu hình Columns**
```jsx
const columns = [
  {
    title: 'Hình ảnh',
    dataIndex: 'image',
    render: (url) => <Image src={url} width={50} height={50} />
  },
  {
    title: 'Mã sản phẩm',
    dataIndex: 'code',
    render: (text) => <Text code>{text}</Text>
  },
  {
    title: 'Tên sản phẩm',
    dataIndex: 'name',
    render: (text, record) => (
      <div>
        <div style={{ fontWeight: 500 }}>{text}</div>
        <Text type="secondary">{record.category}</Text>
      </div>
    )
  },
  {
    title: 'Giá bán',
    dataIndex: 'price',
    render: (price) => (
      <Text strong style={{ color: '#f50' }}>
        {new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(price)}
      </Text>
    )
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    render: (status) => (
      <Tag color={status === 'active' ? 'green' : 'red'}>
        {status === 'active' ? 'Hoạt động' : 'Ngừng bán'}
      </Tag>
    )
  }
];
```

### **4. Sử dụng PageHeader**
```jsx
<PageHeader
  title="Danh sách sản phẩm"
  subTitle="Quản lý thông tin sản phẩm"
  actions={[
    {
      type: 'primary',
      icon: <PlusOutlined />,
      label: 'Thêm sản phẩm',
      onClick: handleCreate
    }
  ]}
  statistics={[
    {
      title: 'Tổng sản phẩm',
      value: 1234,
      valueStyle: { color: '#1890ff' }
    },
    {
      title: 'Đang bán',
      value: 856,
      valueStyle: { color: '#52c41a' }
    }
  ]}
/>
```

## 🎨 **Tính năng chính**

### **DataTable Features:**
- ✅ **Header với title màu xanh** (giống ảnh)
- ✅ **Search box với icon** (giống ảnh)  
- ✅ **Filter dropdown** (giống ảnh)
- ✅ **Nút "Thêm mới" màu xanh** (giống ảnh)
- ✅ **Actions: Edit, Delete, View** với icons
- ✅ **Pagination đầy đủ**
- ✅ **Responsive design**

### **Layout giống như trong ảnh:**
```
┌─────────────────────────────────────────────────────────────┐
│ [Title màu xanh]    [Search] [Filter] [Filter] [Thêm mới]  │
├─────────────────────────────────────────────────────────────┤
│ ☐ | Ảnh | Mã | Tên sản phẩm | Giá | Trạng thái | Thao tác  │
│ ☐ | ... | ... | ...          | ... | ...        | [⚙️📝🗑️] │
│ ☐ | ... | ... | ...          | ... | ...        | [⚙️📝🗑️] │
├─────────────────────────────────────────────────────────────┤
│                    [Pagination controls]                    │
└─────────────────────────────────────────────────────────────┘
```

## 📱 **Responsive Design**
- Mobile: Stack filters vertically
- Tablet: 2 columns layout  
- Desktop: Full horizontal layout

## 🔧 **Customization**
- Thay đổi màu sắc qua CSS variables
- Custom filter types
- Custom action buttons
- Custom column renderers

Bây giờ bạn có thể sử dụng các component này trong toàn bộ ứng dụng để tạo giao diện nhất quán! 🎉
