const express = require("express");
const { asyncHandler } = require("../middleware/errorHandler");
const { requirePermission } = require("../middleware/auth");
const {
  getOverviewReport,
  getProductReport,
  getCustomerReport,
  getBusinessActivityReport,
  getCustomerGroupReport,
} = require("../controllers/reportController");

const router = express.Router();

/**
 * @route GET /api/reports/overview
 * @desc Lấy báo cáo tổng quan
 * @access Private (Cần quyền XEM_BAO_CAO)
 * @query startDate, endDate, period
 */
router.get(
  "/overview",
  requirePermission("XEM_BAO_CAO"),
  asyncHandler(getOverviewReport)
);

/**
 * @route GET /api/reports/products
 * @desc Lấy báo cáo sản phẩm
 * @access Private (Cần quyền XEM_BAO_CAO)
 * @query startDate, endDate, limit
 */
router.get(
  "/products",
  requirePermission("XEM_BAO_CAO"),
  asyncHandler(getProductReport)
);

/**
 * @route GET /api/reports/customers
 * @desc Lấy báo cáo khách hàng
 * @access Private (Cần quyền XEM_BAO_CAO)
 * @query startDate, endDate, limit
 */
router.get(
  "/customers",
  requirePermission("XEM_BAO_CAO"),
  asyncHandler(getCustomerReport)
);

/**
 * @route GET /api/reports/business-activity
 * @desc Lấy báo cáo hoạt động kinh doanh theo khách hàng
 * @access Private (Cần quyền XEM_BAO_CAO)
 * @query startDate, endDate, customerType, limit
 */
router.get(
  "/business-activity",
  requirePermission("XEM_BAO_CAO"),
  asyncHandler(getBusinessActivityReport)
);

/**
 * @route GET /api/reports/customer-groups
 * @desc Lấy báo cáo nhóm khách hàng
 * @access Private (Cần quyền XEM_BAO_CAO)
 * @query startDate, endDate, limit
 */
router.get(
  "/customer-groups",
  requirePermission("XEM_BAO_CAO"),
  asyncHandler(getCustomerGroupReport)
);

module.exports = router;
