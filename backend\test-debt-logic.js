const { sequelize } = require('./models');

async function testDebtLogic() {
  try {
    console.log('🧪 Testing debt calculation logic...');
    
    // Test cases theo yêu cầu: Công nợ = Tiền sản phẩm - Tiền cọc - Tiền COD
    const testCases = [
      {
        name: "Trường hợp 1: Khách nợ tiền",
        tong_phai_tra: 1000000,  // 1 triệu
        tien_coc: 200000,        // 200k cọc
        tien_cod: 300000,        // 300k COD
        expected_debt: 500000,   // Khách còn nợ 500k
        description: "Khách mua 1tr, cọc 200k, COD 300k → còn nợ 500k"
      },
      {
        name: "Trường hợp 2: Đã thanh toán đủ",
        tong_phai_tra: 1000000,  // 1 triệu
        tien_coc: 400000,        // 400k cọc
        tien_cod: 600000,        // 600k COD
        expected_debt: 0,        // Không nợ
        description: "<PERSON>h<PERSON><PERSON> mua 1tr, cọc 400k, COD 600k → đã đủ"
      },
      {
        name: "Trường hợp 3: <PERSON><PERSON><PERSON> nợ kh<PERSON>ch (CTV bán giá cao)",
        tong_phai_tra: 1000000,  // 1 triệu (giá gốc)
        tien_coc: 200000,        // 200k cọc
        tien_cod: 1200000,       // 1.2tr COD (CTV bán giá cao)
        expected_debt: -400000,  // Mình nợ khách 400k
        description: "Sản phẩm 1tr, cọc 200k, COD 1.2tr → mình nợ CTV 400k"
      },
      {
        name: "Trường hợp 4: Chỉ có tiền cọc",
        tong_phai_tra: 500000,   // 500k
        tien_coc: 300000,        // 300k cọc
        tien_cod: 0,             // Không COD
        expected_debt: 200000,   // Khách còn nợ 200k
        description: "Khách mua 500k, cọc 300k → còn nợ 200k"
      },
      {
        name: "Trường hợp 5: Chỉ có COD",
        tong_phai_tra: 800000,   // 800k
        tien_coc: 0,             // Không cọc
        tien_cod: 800000,        // 800k COD
        expected_debt: 0,        // Đã đủ
        description: "Khách mua 800k, COD 800k → đã đủ"
      }
    ];

    console.log('\n📊 Testing debt calculation formula:');
    console.log('💡 Công nợ = Tiền sản phẩm - Tiền cọc - Tiền COD');
    console.log('💡 Nếu âm → Mình nợ khách, Nếu dương → Khách nợ mình\n');

    testCases.forEach((testCase, index) => {
      const { tong_phai_tra, tien_coc, tien_cod, expected_debt } = testCase;
      
      // Tính công nợ theo công thức
      const calculated_debt = tong_phai_tra - tien_coc - tien_cod;
      
      // Kiểm tra kết quả
      const isCorrect = calculated_debt === expected_debt;
      const status = isCorrect ? '✅' : '❌';
      
      console.log(`${status} ${testCase.name}:`);
      console.log(`   📝 ${testCase.description}`);
      console.log(`   💰 Tiền sản phẩm: ${tong_phai_tra.toLocaleString('vi-VN')}đ`);
      console.log(`   🏦 Tiền cọc: ${tien_coc.toLocaleString('vi-VN')}đ`);
      console.log(`   🚚 Tiền COD: ${tien_cod.toLocaleString('vi-VN')}đ`);
      console.log(`   🧮 Công nợ tính được: ${calculated_debt.toLocaleString('vi-VN')}đ`);
      console.log(`   🎯 Công nợ mong đợi: ${expected_debt.toLocaleString('vi-VN')}đ`);
      
      if (calculated_debt > 0) {
        console.log(`   📊 Kết quả: Khách còn nợ ${calculated_debt.toLocaleString('vi-VN')}đ`);
      } else if (calculated_debt < 0) {
        console.log(`   📊 Kết quả: Mình nợ khách ${Math.abs(calculated_debt).toLocaleString('vi-VN')}đ`);
      } else {
        console.log(`   📊 Kết quả: Đã thanh toán đủ`);
      }
      
      if (!isCorrect) {
        console.log(`   ⚠️  FAILED: Expected ${expected_debt}, got ${calculated_debt}`);
      }
      
      console.log('');
    });

    // Test với database thực tế
    console.log('🔍 Testing with real database data...');
    
    const sampleOrder = await sequelize.query(`
      SELECT 
        id, ma_don_hang, tong_phai_tra, tien_coc, tien_cod,
        (tong_phai_tra - tien_coc - tien_cod) as calculated_debt,
        con_phai_tra
      FROM don_hang 
      WHERE tien_coc > 0 OR tien_cod > 0
      LIMIT 3
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    if (sampleOrder.length > 0) {
      console.log('\n📋 Sample orders from database:');
      sampleOrder.forEach((order, index) => {
        console.log(`${index + 1}. Order ${order.ma_don_hang}:`);
        console.log(`   💰 Tổng phải trả: ${order.tong_phai_tra?.toLocaleString('vi-VN') || 0}đ`);
        console.log(`   🏦 Tiền cọc: ${order.tien_coc?.toLocaleString('vi-VN') || 0}đ`);
        console.log(`   🚚 Tiền COD: ${order.tien_cod?.toLocaleString('vi-VN') || 0}đ`);
        console.log(`   🧮 Công nợ tính được: ${order.calculated_debt?.toLocaleString('vi-VN') || 0}đ`);
        console.log(`   📊 Con phải trả (DB): ${order.con_phai_tra?.toLocaleString('vi-VN') || 0}đ`);
        console.log('');
      });
    } else {
      console.log('📝 No orders with deposit or COD found in database');
    }

    console.log('✅ Debt logic test completed!');
    
  } catch (error) {
    console.error('❌ Error testing debt logic:', error);
  } finally {
    await sequelize.close();
  }
}

testDebtLogic();
