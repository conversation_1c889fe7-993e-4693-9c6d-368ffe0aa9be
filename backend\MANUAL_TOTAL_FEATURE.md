# 💰 Tính Năng Tổng Tiền Thủ Công

## 🎯 Mục Tiêu
Cho phép điều chỉnh tổng tiền đơn hàng một cách linh hoạt khi tạo đơn hàng, không bắt buộc phải tính theo giá sản phẩm x số lượng.

## 🔍 Lý Do Cần Thiết

### **Các Trường Hợp Thực Tế:**
- ✅ **B<PERSON> thấp hơn giá niêm yết** cho khách VIP
- ✅ **Khách đã thanh toán một phần** trước khi tạo đơn
- ✅ **Giảm giá đặc biệt** không theo quy tắc cố định
- ✅ **Combo/package deal** với giá ưu đãi
- ✅ **Điều chỉnh theo thương lượng** với khách hàng

## ✅ Những Gì Đã Hoàn Thành

### 1. **Frontend Implementation**

#### **CreateOrder.jsx**
- ✅ **Switch toggle** để chuyển đổi giữa chế độ tự động và thủ công
- ✅ **InputNumber** cho phép nhập tổng tiền thủ công
- ✅ **Ghi chú giải thích** khi ở chế độ thủ công
- ✅ **Logic tính toán** linh hoạt theo chế độ

#### **UI Components**
```javascript
// Switch để toggle chế độ
<Switch
  size="small"
  checked={isManualTotalMode}
  onChange={handleManualTotalToggle}
  checkedChildren="Thủ công"
  unCheckedChildren="Tự động"
/>

// Input để nhập tổng tiền thủ công
<InputNumber
  value={manualTotal}
  onChange={handleManualTotalChange}
  min={0}
  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
  parser={value => value.replace(/\$\s?|(,*)/g, '')}
  addonAfter="đ"
/>
```

### 2. **Backend Implementation**

#### **orderController.js**
- ✅ **Nhận parameter** `is_manual_total` từ frontend
- ✅ **Logic tính toán** linh hoạt theo chế độ
- ✅ **Ghi log** chi tiết quá trình tính toán
- ✅ **Ghi chú tự động** vào đơn hàng khi dùng manual total

#### **Logic Tính Toán**
```javascript
// Sử dụng tổng tiền thủ công nếu được chỉ định
const tong_phai_tra = is_manual_total 
  ? tong_thanh_toan 
  : (calculated_total - chiet_khau_amount);

// Ghi chú tự động
if (is_manual_total) {
  const manual_note = `[TỔNG TIỀN THỦ CÔNG] Tổng tiền được điều chỉnh thủ công: ${tong_thanh_toan.toLocaleString('vi-VN')}đ (Tính tự động: ${(calculated_total - chiet_khau_amount).toLocaleString('vi-VN')}đ)`;
}
```

## 🚀 Cách Sử Dụng

### **1. Chế Độ Tự Động (Mặc định)**
1. Thêm sản phẩm vào đơn hàng
2. Hệ thống tự động tính: `Tổng tiền = (Số lượng × Đơn giá) - Giảm giá`
3. Tổng tiền hiển thị màu xanh, không thể chỉnh sửa

### **2. Chế Độ Thủ Công**
1. **Bật switch "Thủ công"** ở phần "Khách phải trả"
2. **Nhập tổng tiền mong muốn** vào ô input
3. **Thêm ghi chú** giải thích lý do (khuyến khích)
4. **Tạo đơn hàng** như bình thường

### **3. Các Trường Hợp Sử Dụng**

#### **Trường hợp 1: Khách VIP giảm giá**
```
Sản phẩm: Laptop × 1 = 20,000,000đ
Giảm giá VIP: 2,000,000đ
→ Chọn "Thủ công" → Nhập: 18,000,000đ
```

#### **Trường hợp 2: Khách đã cọc trước**
```
Sản phẩm: Điện thoại × 2 = 30,000,000đ
Đã cọc trước: 10,000,000đ
→ Chọn "Thủ công" → Nhập: 20,000,000đ
```

#### **Trường hợp 3: Combo deal**
```
Sản phẩm: Laptop + Chuột + Bàn phím = 25,000,000đ
Giá combo: 22,000,000đ
→ Chọn "Thủ công" → Nhập: 22,000,000đ
```

## 📊 Dữ Liệu Được Lưu

### **Database Fields**
- ✅ **`tong_tien`**: Tổng tiền hàng (tính từ sản phẩm)
- ✅ **`chiet_khau`**: Chiết khấu/giảm giá
- ✅ **`tong_phai_tra`**: Tổng tiền cuối cùng (manual hoặc auto)
- ✅ **`ghi_chu`**: Ghi chú có thông tin manual total

### **Ghi Chú Tự Động**
Khi sử dụng manual total, hệ thống tự động thêm ghi chú:
```
[TỔNG TIỀN THỦ CÔNG] Tổng tiền được điều chỉnh thủ công: 18,000,000đ (Tính tự động: 20,000,000đ)
```

## 🔍 Logging & Tracking

### **Console Logs**
```javascript
💰 Order total calculation: {
  calculated_total: 200000,
  chiet_khau_amount: 0,
  is_manual_total: true,
  manual_total: 180000,
  final_total: 180000
}
```

### **Audit Trail**
- ✅ Ghi rõ trong `ghi_chu` đơn hàng
- ✅ Log chi tiết trong console
- ✅ Có thể trace lại lý do điều chỉnh

## 🧪 Testing

### **Test Script**
```bash
cd SaleSysBE
node test-manual-total.js
```

### **Test Cases**
1. ✅ **Đơn hàng tự động** (bình thường)
2. ✅ **Đơn hàng thủ công** (giảm giá đặc biệt)
3. ✅ **Đơn hàng thủ công** (khách đã trả trước)

### **Expected Results**
- ✅ Tổng tiền được tính đúng theo chế độ
- ✅ Ghi chú tự động được thêm vào
- ✅ Database lưu đúng thông tin

## 🎯 Lợi Ích

### **1. Tính Linh Hoạt**
- ✅ Phù hợp với thực tế kinh doanh
- ✅ Không bị ràng buộc bởi giá niêm yết
- ✅ Có thể thương lượng với khách hàng

### **2. Tính Minh Bạch**
- ✅ Ghi rõ lý do điều chỉnh
- ✅ Có thể trace lại lịch sử
- ✅ Audit trail đầy đủ

### **3. User Experience**
- ✅ Dễ sử dụng với switch toggle
- ✅ Giao diện trực quan
- ✅ Ghi chú hướng dẫn rõ ràng

## 🔮 Tính Năng Mở Rộng

### **Có Thể Thêm Sau**
- **Phân quyền** cho manual total (chỉ manager mới được dùng)
- **Giới hạn % giảm giá** tối đa
- **Approval workflow** cho đơn hàng manual total lớn
- **Báo cáo** thống kê manual total theo thời gian
- **Template** các mức giảm giá thường dùng

## 🎉 Kết Luận

✅ **Tính năng hoàn chỉnh và sẵn sàng sử dụng**
✅ **Phù hợp với thực tế kinh doanh**
✅ **Giao diện thân thiện và dễ sử dụng**
✅ **Có audit trail và tracking đầy đủ**
✅ **Tested và stable**

**Tính năng tổng tiền thủ công giúp hệ thống linh hoạt hơn trong việc xử lý các trường hợp đặc biệt trong kinh doanh thực tế!** 🚀
