import React, { useState } from 'react';
import { Button, message, Card, Space, Alert } from 'antd';

const DebugUpload = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState([]);

  const addResult = (type, message, data = null) => {
    const result = {
      id: Date.now(),
      type, // 'success', 'error', 'info'
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    };
    setResults(prev => [result, ...prev]);
  };

  const testBackendConnection = async () => {
    setTesting(true);
    addResult('info', 'Testing backend connection...');

    try {
      // Test 1: Basic backend health
      const healthResponse = await fetch('http://localhost:5000/api/health');
      if (healthResponse.ok) {
        addResult('success', '✅ Backend health check passed');
      } else {
        addResult('error', '❌ Backend health check failed');
      }

      // Test 2: Upload route test
      const uploadTestResponse = await fetch('http://localhost:5000/api/upload/test');
      if (uploadTestResponse.ok) {
        const data = await uploadTestResponse.json();
        addResult('success', '✅ Upload routes accessible', data);
      } else {
        addResult('error', '❌ Upload routes not accessible');
      }

      // Test 3: CORS test
      addResult('info', '🌐 CORS test - if you see this, CORS is working');

    } catch (error) {
      addResult('error', `❌ Connection failed: ${error.message}`);
    } finally {
      setTesting(false);
    }
  };

  const testFileUpload = async () => {
    setTesting(true);
    addResult('info', 'Testing file upload...');

    try {
      // Create a minimal test image (1x1 pixel PNG)
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#FF0000';
      ctx.fillRect(0, 0, 1, 1);

      canvas.toBlob(async (blob) => {
        try {
          const formData = new FormData();
          formData.append('image', blob, 'test.png');
          formData.append('folder', 'debug-test');

          addResult('info', '📤 Sending upload request...');

          const response = await fetch('http://localhost:5000/api/upload/single', {
            method: 'POST',
            body: formData,
          });

          addResult('info', `📡 Response status: ${response.status}`);

          if (response.ok) {
            const data = await response.json();
            addResult('success', '✅ Upload successful!', data);
          } else {
            const errorText = await response.text();
            addResult('error', `❌ Upload failed: ${response.status} ${errorText}`);
          }

        } catch (error) {
          addResult('error', `❌ Upload error: ${error.message}`);
        } finally {
          setTesting(false);
        }
      }, 'image/png');

    } catch (error) {
      addResult('error', `❌ Test setup failed: ${error.message}`);
      setTesting(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <Card title="🔧 Debug Upload System" style={{ maxWidth: 800, margin: '20px auto' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Alert
          message="Debug Tools"
          description="Use these tools to diagnose upload issues step by step."
          type="info"
          showIcon
        />

        <Space>
          <Button 
            type="primary" 
            onClick={testBackendConnection}
            loading={testing}
          >
            Test Backend Connection
          </Button>
          
          <Button 
            onClick={testFileUpload}
            loading={testing}
          >
            Test File Upload
          </Button>
          
          <Button onClick={clearResults}>
            Clear Results
          </Button>
        </Space>

        <div style={{ marginTop: 20 }}>
          <h4>📋 Test Results:</h4>
          {results.length === 0 ? (
            <p style={{ color: '#999' }}>No tests run yet</p>
          ) : (
            <div style={{ maxHeight: 400, overflowY: 'auto' }}>
              {results.map(result => (
                <div 
                  key={result.id}
                  style={{
                    padding: 8,
                    margin: '4px 0',
                    borderRadius: 4,
                    backgroundColor: 
                      result.type === 'success' ? '#f6ffed' :
                      result.type === 'error' ? '#fff2f0' : '#f0f9ff',
                    border: `1px solid ${
                      result.type === 'success' ? '#b7eb8f' :
                      result.type === 'error' ? '#ffccc7' : '#91d5ff'
                    }`
                  }}
                >
                  <div style={{ fontSize: 12, color: '#666' }}>
                    {result.timestamp}
                  </div>
                  <div>{result.message}</div>
                  {result.data && (
                    <pre style={{ 
                      fontSize: 10, 
                      background: '#f5f5f5', 
                      padding: 4, 
                      borderRadius: 2,
                      marginTop: 4,
                      maxHeight: 100,
                      overflow: 'auto'
                    }}>
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        <Alert
          message="Expected Results"
          description={
            <div>
              <p><strong>✅ Success:</strong> All tests should pass</p>
              <p><strong>❌ Common Issues:</strong></p>
              <ul>
                <li>Backend not running → Start with `npm run dev`</li>
                <li>CORS errors → Check server.js CORS config</li>
                <li>404 errors → Check route registration</li>
                <li>Upload errors → Check Cloudinary config</li>
              </ul>
            </div>
          }
          type="warning"
        />
      </Space>
    </Card>
  );
};

export default DebugUpload;
