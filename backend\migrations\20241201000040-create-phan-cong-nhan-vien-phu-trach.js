'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('phan_cong_nhan_vien_phu_trach', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      nguoi_dung_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      nhan_vien_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      ngay_phan_cong: {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('phan_cong_nhan_vien_phu_trach', ['nguoi_dung_id']);
    await queryInterface.addIndex('phan_cong_nhan_vien_phu_trach', ['nhan_vien_id']);
    
    // Add unique constraint to prevent duplicate assignments
    await queryInterface.addIndex('phan_cong_nhan_vien_phu_trach', ['nguoi_dung_id', 'nhan_vien_id'], {
      unique: true,
      name: 'unique_phan_cong_nhan_vien'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('phan_cong_nhan_vien_phu_trach');
  }
};