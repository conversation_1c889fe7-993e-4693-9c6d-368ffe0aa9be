/**
 * Utility functions for safe array operations
 */

/**
 * Ensures the input is an array, returns empty array if not
 * @param {any} data - The data to check
 * @returns {Array} - Safe array
 */
export const ensureArray = (data) => {
  return Array.isArray(data) ? data : [];
};

/**
 * Safely maps over an array, returns empty array if input is not an array
 * @param {any} data - The data to map over
 * @param {Function} mapFn - The mapping function
 * @returns {Array} - Mapped array or empty array
 */
export const safeMap = (data, mapFn) => {
  return Array.isArray(data) ? data.map(mapFn) : [];
};

/**
 * Safely filters an array, returns empty array if input is not an array
 * @param {any} data - The data to filter
 * @param {Function} filterFn - The filter function
 * @returns {Array} - Filtered array or empty array
 */
export const safeFilter = (data, filterFn) => {
  return Array.isArray(data) ? data.filter(filterFn) : [];
};

/**
 * Safely reduces an array, returns defaultValue if input is not an array
 * @param {any} data - The data to reduce
 * @param {Function} reduceFn - The reduce function
 * @param {any} initialValue - The initial value
 * @returns {any} - Reduced value or initial value
 */
export const safeReduce = (data, reduceFn, initialValue) => {
  return Array.isArray(data) ? data.reduce(reduceFn, initialValue) : initialValue;
};

/**
 * Safely gets array length, returns 0 if input is not an array
 * @param {any} data - The data to get length from
 * @returns {number} - Array length or 0
 */
export const safeLength = (data) => {
  return Array.isArray(data) ? data.length : 0;
};

/**
 * Safely finds an item in array, returns undefined if input is not an array
 * @param {any} data - The data to search in
 * @param {Function} findFn - The find function
 * @returns {any} - Found item or undefined
 */
export const safeFind = (data, findFn) => {
  return Array.isArray(data) ? data.find(findFn) : undefined;
};

/**
 * Safely checks if array includes an item, returns false if input is not an array
 * @param {any} data - The data to check
 * @param {any} item - The item to look for
 * @returns {boolean} - Whether item is included or false
 */
export const safeIncludes = (data, item) => {
  return Array.isArray(data) ? data.includes(item) : false;
};

/**
 * Safely gets an item at index, returns undefined if input is not an array
 * @param {any} data - The data to get from
 * @param {number} index - The index to get
 * @returns {any} - Item at index or undefined
 */
export const safeGet = (data, index) => {
  return Array.isArray(data) ? data[index] : undefined;
};

/**
 * Safely sorts an array, returns empty array if input is not an array
 * @param {any} data - The data to sort
 * @param {Function} sortFn - The sort function (optional)
 * @returns {Array} - Sorted array or empty array
 */
export const safeSort = (data, sortFn) => {
  if (!Array.isArray(data)) return [];
  return sortFn ? [...data].sort(sortFn) : [...data].sort();
};

/**
 * Safely slices an array, returns empty array if input is not an array
 * @param {any} data - The data to slice
 * @param {number} start - Start index
 * @param {number} end - End index (optional)
 * @returns {Array} - Sliced array or empty array
 */
export const safeSlice = (data, start, end) => {
  return Array.isArray(data) ? data.slice(start, end) : [];
};

/**
 * Safely concatenates arrays, returns empty array if inputs are not arrays
 * @param {...any} arrays - Arrays to concatenate
 * @returns {Array} - Concatenated array or empty array
 */
export const safeConcat = (...arrays) => {
  const validArrays = arrays.filter(arr => Array.isArray(arr));
  return validArrays.length > 0 ? [].concat(...validArrays) : [];
};

/**
 * Safely checks if data is empty array or not an array
 * @param {any} data - The data to check
 * @returns {boolean} - Whether data is empty or not an array
 */
export const isEmpty = (data) => {
  return !Array.isArray(data) || data.length === 0;
};

/**
 * Safely checks if data is a non-empty array
 * @param {any} data - The data to check
 * @returns {boolean} - Whether data is a non-empty array
 */
export const isNotEmpty = (data) => {
  return Array.isArray(data) && data.length > 0;
};

/**
 * Creates a safe array from API response data
 * @param {any} response - API response
 * @param {string} dataPath - Path to data in response (default: 'data')
 * @returns {Array} - Safe array from response
 */
export const createSafeArrayFromResponse = (response, dataPath = 'data') => {
  if (!response) return [];
  
  const data = dataPath.split('.').reduce((obj, key) => obj?.[key], response);
  return ensureArray(data);
};

/**
 * Default export object with all functions
 */
export default {
  ensureArray,
  safeMap,
  safeFilter,
  safeReduce,
  safeLength,
  safeFind,
  safeIncludes,
  safeGet,
  safeSort,
  safeSlice,
  safeConcat,
  isEmpty,
  isNotEmpty,
  createSafeArrayFromResponse
};
