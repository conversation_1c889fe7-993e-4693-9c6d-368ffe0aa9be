import React from 'react';
import {
  Button,
  <PERSON>,
  Dropdown,
  Typography,
  Breadcrumb,
  Tag,
  Avatar,
  Statistic,
  Row,
  Col,
  Card
} from 'antd';
import {
  PlusOutlined,
  DownloadOutlined,
  UploadOutlined,
  MoreOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

const PageHeader = ({
  // Basic props
  title,
  subTitle,
  
  // Navigation
  onBack,
  breadcrumb,
  
  // Actions
  actions = [],
  primaryAction,
  
  // Content
  tags,
  avatar,
  extra,
  
  // Statistics
  statistics = [],
  
  // Styling
  className,
  style,
  
  // Layout
  ghost = false
}) => {
  // Build breadcrumb items
  const breadcrumbItems = breadcrumb ? {
    items: breadcrumb.map(item => ({
      title: item.href ? (
        <a href={item.href}>{item.title}</a>
      ) : item.title
    }))
  } : undefined;

  // Build action buttons
  const renderActions = () => {
    const allActions = [...actions];
    
    // Add primary action if provided
    if (primaryAction) {
      allActions.unshift({
        ...primaryAction,
        type: 'primary'
      });
    }

    if (allActions.length === 0) return null;

    // If too many actions, group some in dropdown
    if (allActions.length > 3) {
      const primaryActions = allActions.slice(0, 2);
      const secondaryActions = allActions.slice(2);

      return (
        <Space>
          {primaryActions.map((action, index) => (
            <Button
              key={index}
              type={action.type}
              icon={action.icon}
              onClick={action.onClick}
              loading={action.loading}
              disabled={action.disabled}
              danger={action.danger}
            >
              {action.label}
            </Button>
          ))}
          <Dropdown
            menu={{
              items: secondaryActions.map((action, index) => ({
                key: index,
                label: action.label,
                icon: action.icon,
                disabled: action.disabled,
                danger: action.danger,
                onClick: action.onClick
              }))
            }}
            trigger={['click']}
          >
            <Button icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      );
    }

    return (
      <Space>
        {allActions.map((action, index) => (
          <Button
            key={index}
            type={action.type}
            icon={action.icon}
            onClick={action.onClick}
            loading={action.loading}
            disabled={action.disabled}
            danger={action.danger}
          >
            {action.label}
          </Button>
        ))}
      </Space>
    );
  };

  // Render tags
  const renderTags = () => {
    if (!tags || tags.length === 0) return null;
    
    return (
      <Space>
        {tags.map((tag, index) => (
          <Tag key={index} color={tag.color}>
            {tag.label}
          </Tag>
        ))}
      </Space>
    );
  };

  // Render statistics
  const renderStatistics = () => {
    if (!statistics || statistics.length === 0) return null;

    return (
      <Row gutter={16} style={{ marginTop: 16 }}>
        {statistics.map((stat, index) => (
          <Col key={index} span={6}>
            <Statistic
              title={stat.title}
              value={stat.value}
              precision={stat.precision}
              valueStyle={stat.valueStyle}
              prefix={stat.prefix}
              suffix={stat.suffix}
              formatter={stat.formatter}
            />
          </Col>
        ))}
      </Row>
    );
  };

  return (
    <div className={className} style={style}>
      {/* Breadcrumb */}
      {breadcrumbItems && (
        <Breadcrumb
          items={breadcrumbItems.items}
          style={{ marginBottom: 16 }}
        />
      )}

      {/* Header Content */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 16
      }}>
        <div style={{ flex: 1 }}>
          {/* Back button */}
          {onBack && (
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={onBack}
              style={{ marginBottom: 8 }}
            >
              Quay lại
            </Button>
          )}

          {/* Title section */}
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
            {avatar && <Avatar {...avatar} style={{ marginRight: 12 }} />}
            <div>
              <Title level={3} style={{ margin: 0 }}>
                {title}
              </Title>
              {subTitle && (
                <Text type="secondary" style={{ fontSize: 14 }}>
                  {subTitle}
                </Text>
              )}
            </div>
          </div>

          {/* Tags */}
          {renderTags()}
        </div>

        {/* Actions */}
        <div style={{ marginLeft: 16 }}>
          {renderActions()}
        </div>
      </div>

      {/* Extra content */}
      {extra}

      {/* Statistics */}
      {renderStatistics()}
    </div>
  );
};

// Common action presets
PageHeader.Actions = {
  // Create action
  create: (onClick, loading = false) => ({
    type: 'primary',
    icon: <PlusOutlined />,
    label: 'Thêm mới',
    onClick,
    loading
  }),

  // Export action
  export: (onClick, loading = false) => ({
    icon: <DownloadOutlined />,
    label: 'Xuất dữ liệu',
    onClick,
    loading
  }),

  // Import action
  import: (onClick, loading = false) => ({
    icon: <UploadOutlined />,
    label: 'Nhập dữ liệu',
    onClick,
    loading
  }),

  // Back action
  back: (onClick) => ({
    icon: <ArrowLeftOutlined />,
    label: 'Quay lại',
    onClick
  })
};

// Common tag presets
PageHeader.Tags = {
  status: {
    active: { label: 'Hoạt động', color: 'green' },
    inactive: { label: 'Không hoạt động', color: 'red' },
    pending: { label: 'Chờ xử lý', color: 'orange' },
    draft: { label: 'Nháp', color: 'default' }
  },
  
  priority: {
    high: { label: 'Cao', color: 'red' },
    medium: { label: 'Trung bình', color: 'orange' },
    low: { label: 'Thấp', color: 'blue' }
  }
};

export default PageHeader;
