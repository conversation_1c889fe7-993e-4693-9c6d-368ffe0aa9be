import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Typography,
  DatePicker,
  Button,
  Space,
  Select,
  Table,
  Statistic,
  Progress,
  Divider,
  message
} from 'antd';
import {
  Bar<PERSON><PERSON>Outlined,
  Pie<PERSON>hartOutlined,
  RiseOutlined,
  DownloadOutlined,
  FilterOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';
import { testAPI } from '../../services/api';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// Hook để lấy dữ liệu báo cáo
const useDebtReport = (filters) => {
  return useQuery(
    ['debt-report', filters],
    async () => {
      const queryParams = new URLSearchParams();
      
      if (filters.dateRange?.[0]) queryParams.append('start_date', filters.dateRange[0]);
      if (filters.dateRange?.[1]) queryParams.append('end_date', filters.dateRange[1]);
      if (filters.reportType) queryParams.append('type', filters.reportType);

      const response = await testAPI.getDebtReport();
      return response;
    },
    {
      keepPreviousData: true,
      refetchOnWindowFocus: false
    }
  );
};

const DebtReport = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState({
    dateRange: [dayjs().subtract(30, 'day'), dayjs()],
    reportType: 'overview',
    timePeriod: 'month' // month, quarter, year
  });

  const { data, isLoading, refetch } = useDebtReport(filters);

  // Xử lý click vào khách hàng để xem chi tiết
  const handleCustomerClick = (customerId) => {
    navigate(`/accounting/debt/${customerId}`);
  };

  // Tạo dữ liệu test
  const handleSeedTestData = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/debt/seed-test-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        message.success('Tạo dữ liệu test thành công!');
        refetch(); // Refresh data
      } else {
        message.error('Lỗi khi tạo dữ liệu test');
      }
    } catch (error) {
      console.error('Error seeding test data:', error);
      message.error('Lỗi kết nối khi tạo dữ liệu test');
    }
  };

  // Sử dụng dữ liệu từ API, fallback về empty arrays nếu không có data
  const chartData = data?.chartData || [];
  const pieData = data?.pieData || [];
  const topDebtors = data?.topDebtors || [];
  const stats = data?.stats || {
    totalDebt: 0,
    overdueDebt: 0,
    collectionRate: 0,
    avgDebtPerCustomer: 0
  };

  const debtorColumns = [
    {
      title: 'Hạng',
      dataIndex: 'rank',
      key: 'rank',
      width: 60,
      align: 'center'
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customer',
      key: 'customer',
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => handleCustomerClick(record.customerId)}
          style={{ padding: 0, height: 'auto' }}
        >
          {text}
        </Button>
      )
    },
    {
      title: 'Công nợ',
      dataIndex: 'debt',
      key: 'debt',
      align: 'right',
      render: (value) => new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    },
    {
      title: 'Số đơn',
      dataIndex: 'orders',
      key: 'orders',
      align: 'center'
    }
  ];

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <BarChartOutlined style={{ marginRight: '8px' }} />
          Báo cáo công nợ
        </Title>
      </div>

      {/* Filters */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong>Khoảng thời gian</Text>
              <RangePicker
                value={filters.dateRange}
                onChange={(dates) => handleFilterChange('dateRange', dates)}
                format="DD/MM/YYYY"
                style={{ width: '100%' }}
              />
            </Space>
          </Col>
          <Col span={4}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong>Loại báo cáo</Text>
              <Select
                value={filters.reportType}
                onChange={(value) => handleFilterChange('reportType', value)}
                style={{ width: '100%' }}
              >
                <Option value="overview">Tổng quan</Option>
                <Option value="detailed">Chi tiết</Option>
                <Option value="aging">Phân tích độ tuổi nợ</Option>
              </Select>
            </Space>
          </Col>
          <Col span={4}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong>Chu kỳ thời gian</Text>
              <Select
                value={filters.timePeriod}
                onChange={(value) => handleFilterChange('timePeriod', value)}
                style={{ width: '100%' }}
              >
                <Option value="month">Theo tháng</Option>
                <Option value="quarter">Theo quý</Option>
                <Option value="year">Theo năm</Option>
              </Select>
            </Space>
          </Col>
          <Col span={4}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong>Bộ lọc nhanh</Text>
              <Select
                placeholder="Chọn khoảng thời gian"
                onChange={(value) => {
                  let newDateRange;
                  switch(value) {
                    case 'this_month':
                      newDateRange = [dayjs().startOf('month'), dayjs().endOf('month')];
                      break;
                    case 'last_month':
                      newDateRange = [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')];
                      break;
                    case 'this_quarter':
                      newDateRange = [dayjs().startOf('quarter'), dayjs().endOf('quarter')];
                      break;
                    case 'this_year':
                      newDateRange = [dayjs().startOf('year'), dayjs().endOf('year')];
                      break;
                    default:
                      return;
                  }
                  handleFilterChange('dateRange', newDateRange);
                }}
                style={{ width: '100%' }}
              >
                <Option value="this_month">Tháng này</Option>
                <Option value="last_month">Tháng trước</Option>
                <Option value="this_quarter">Quý này</Option>
                <Option value="this_year">Năm này</Option>
              </Select>
            </Space>
          </Col>
          <Col span={6}>
            <Space>
              <Button icon={<FilterOutlined />}>
                Bộ lọc nâng cao
              </Button>
              <Button type="primary" icon={<DownloadOutlined />}>
                Xuất báo cáo
              </Button>
              <Button
                type="dashed"
                onClick={handleSeedTestData}
                style={{ marginLeft: 8 }}
              >
                Tạo dữ liệu test
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Statistics Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng công nợ"
              value={stats.totalDebt}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Nợ quá hạn"
              value={stats.overdueDebt}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#ff7875' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tỷ lệ thu hồi"
              value={stats.collectionRate}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
            <Progress 
              percent={stats.collectionRate} 
              showInfo={false} 
              strokeColor="#52c41a"
              style={{ marginTop: '8px' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Nợ TB/Khách hàng"
              value={stats.avgDebtPerCustomer}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={16}>
          <Card title="Biến động công nợ theo tháng">
            {chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis
                    tickFormatter={(value) =>
                      new Intl.NumberFormat('vi-VN', {
                        notation: 'compact',
                        compactDisplay: 'short'
                      }).format(value)
                    }
                  />
                  <Tooltip
                    formatter={(value) =>
                      new Intl.NumberFormat('vi-VN', {
                        style: 'currency',
                        currency: 'VND'
                      }).format(value)
                    }
                  />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="debt"
                    stackId="1"
                    stroke="#ff4d4f"
                    fill="#ff4d4f"
                    name="Công nợ"
                  />
                  <Area
                    type="monotone"
                    dataKey="paid"
                    stackId="1"
                    stroke="#52c41a"
                    fill="#52c41a"
                    name="Đã thu"
                  />
                </AreaChart>
              </ResponsiveContainer>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px 0' }}>
                <Text type="secondary">Không có dữ liệu biểu đồ</Text>
                <br />
                <Button
                  type="link"
                  onClick={handleSeedTestData}
                  style={{ marginTop: 8 }}
                >
                  Tạo dữ liệu test để xem biểu đồ
                </Button>
              </div>
            )}
          </Card>
        </Col>
        <Col span={8}>
          <Card title="Tỷ lệ thanh toán">
            {pieData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px 0' }}>
                <Text type="secondary">Không có dữ liệu tỷ lệ</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* Top Debtors */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="Top khách hàng có nợ cao nhất">
            <Table
              columns={debtorColumns}
              dataSource={topDebtors}
              rowKey="rank"
              pagination={false}
              size="small"
              locale={{
                emptyText: (
                  <div style={{ textAlign: 'center', padding: '20px 0' }}>
                    <Text type="secondary">Không có dữ liệu khách hàng</Text>
                    <br />
                    <Button
                      type="link"
                      onClick={handleSeedTestData}
                      style={{ marginTop: 8 }}
                    >
                      Tạo dữ liệu test
                    </Button>
                  </div>
                )
              }}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Phân tích độ tuổi nợ">
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>0-30 ngày</Text>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Progress percent={45} showInfo={false} strokeColor="#52c41a" />
                    <Text>45%</Text>
                  </div>
                </div>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>31-60 ngày</Text>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Progress percent={30} showInfo={false} strokeColor="#faad14" />
                    <Text>30%</Text>
                  </div>
                </div>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>61-90 ngày</Text>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Progress percent={15} showInfo={false} strokeColor="#ff7875" />
                    <Text>15%</Text>
                  </div>
                </div>
                <div>
                  <Text strong>Trên 90 ngày</Text>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Progress percent={10} showInfo={false} strokeColor="#ff4d4f" />
                    <Text>10%</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DebtReport;
