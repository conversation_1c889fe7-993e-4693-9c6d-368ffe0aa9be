'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Thêm cột tiền cọc
    await queryInterface.addColumn('don_hang', 'tien_coc', {
      type: Sequelize.DOUBLE,
      allowNull: true,
      defaultValue: 0,
      comment: 'Tiền cọc khách hàng đã trả'
    });

    // Thêm cột tiền COD
    await queryInterface.addColumn('don_hang', 'tien_cod', {
      type: Sequelize.DOUBLE,
      allowNull: true,
      defaultValue: 0,
      comment: 'Tiền COD (thu hộ)'
    });

    console.log('✅ Added tien_coc and tien_cod columns to don_hang table');
  },

  async down(queryInterface, Sequelize) {
    // Xóa cột tiền COD
    await queryInterface.removeColumn('don_hang', 'tien_cod');
    
    // X<PERSON>a cột tiền cọc
    await queryInterface.removeColumn('don_hang', 'tien_coc');

    console.log('✅ Removed tien_coc and tien_cod columns from don_hang table');
  }
};
