import React, { useState, useEffect } from 'react';
import {
  Card,
  Switch,
  Typography,
  Row,
  Col,
  InputNumber,
  Space,
  Tooltip
} from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

const InlineWarehouseManager = ({ 
  enabled = false,
  onEnabledChange,
  warehouses = [],
  inventoryData = {},
  onInventoryChange
}) => {
  const [localInventory, setLocalInventory] = useState({});

  useEffect(() => {
    setLocalInventory(inventoryData);
  }, [inventoryData]);

  const handleEnabledChange = (checked) => {
    onEnabledChange(checked);
    if (!checked) {
      // Clear inventory data when disabled
      setLocalInventory({});
      onInventoryChange({});
    }
  };

  const handleInventoryChange = (warehouseId, field, value) => {
    const newInventory = {
      ...localInventory,
      [warehouseId]: {
        ...localInventory[warehouseId],
        [field]: value || 0
      }
    };
    setLocalInventory(newInventory);
    onInventoryChange(newInventory);
  };

  return (
    <Card 
      title={
        <Space>
          <span>Khởi tạo kho hàng</span>
          <Tooltip title="Ghi nhận số lượng tồn kho ban đầu và giá vốn của sản phẩm tại các chi nhánh">
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
          </Tooltip>
        </Space>
      }
      style={{ marginBottom: 24 }}
    >
      <div style={{ marginBottom: 16 }}>
        <Space align="start">
          <Switch 
            checked={enabled}
            onChange={handleEnabledChange}
          />
          <div>
            <Text style={{ fontWeight: 500 }}>
              Ghi nhận số lượng Tồn kho ban đầu và Giá vốn của sản phẩm tại các Chi nhánh
            </Text>
          </div>
        </Space>
      </div>

      {enabled && (
        <div style={{ 
          background: '#fafafa',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #e8e8e8'
        }}>
          {/* Header Row */}
          <Row gutter={16} style={{ marginBottom: 12 }}>
            <Col span={8}>
              <Text strong style={{ fontSize: '14px' }}>Chi nhánh</Text>
            </Col>
            <Col span={8}>
              <Text strong style={{ fontSize: '14px' }}>
                Tồn kho ban đầu
              </Text>
            </Col>
            <Col span={8}>
              <Text strong style={{ fontSize: '14px' }}>
                Giá vốn
                <Tooltip title="Giá vốn của sản phẩm">
                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                </Tooltip>
              </Text>
            </Col>
          </Row>

          {/* Warehouse Rows */}
          {Array.isArray(warehouses) && warehouses.map((warehouse) => (
            <Row key={warehouse?.id} gutter={16} style={{ marginBottom: 12 }}>
              <Col span={8}>
                <div style={{
                  padding: '8px 12px',
                  background: '#f5f5f5',
                  borderRadius: '4px',
                  border: '1px solid #d9d9d9',
                  minHeight: '32px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <Text>{warehouse?.ten_kho}</Text>
                </div>
              </Col>
              <Col span={8}>
                <InputNumber
                  value={localInventory[warehouse?.id]?.ton_kho || 0}
                  onChange={(value) => handleInventoryChange(warehouse?.id, 'ton_kho', value)}
                  placeholder="0"
                  style={{ width: '100%' }}
                  min={0}
                  precision={0}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value.replace(/\$\s?|(,*)/g, '')}
                />
              </Col>
              <Col span={8}>
                <InputNumber
                  value={localInventory[warehouse?.id]?.gia_von || 0}
                  onChange={(value) => handleInventoryChange(warehouse?.id, 'gia_von', value)}
                  placeholder="0"
                  style={{ width: '100%' }}
                  min={0}
                  precision={0}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value.replace(/\$\s?|(,*)/g, '')}
                />
              </Col>
            </Row>
          ))}

          {(!Array.isArray(warehouses) || warehouses.length === 0) && (
            <div style={{ 
              textAlign: 'center', 
              padding: '20px 0', 
              color: '#999',
              fontStyle: 'italic'
            }}>
              Không có kho hàng nào để khởi tạo
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default InlineWarehouseManager;
