import React, { useState } from 'react';
import {
  Card,
  Input,
  Tag,
  Button,
  Space,
  Typography,
  Row,
  Col,
  message,
  Tooltip
} from 'antd';
import { PlusOutlined, CloseOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

const InlineAttributeManager = ({ 
  attributes = [], 
  onChange,
  onAttributesChange 
}) => {
  const [newAttributeName, setNewAttributeName] = useState('');
  const [newAttributeValues, setNewAttributeValues] = useState({});

  const addAttribute = () => {
    if (!newAttributeName.trim()) {
      message.error('Vui lòng nhập tên thuộc tính');
      return;
    }

    const newAttribute = {
      ten: newAttributeName.trim(),
      gia_tri: []
    };

    const updatedAttributes = [...attributes, newAttribute];
    onAttributesChange(updatedAttributes);
    setNewAttributeName('');
  };

  const removeAttribute = (index) => {
    const updatedAttributes = attributes.filter((_, i) => i !== index);
    onAttributesChange(updatedAttributes);
  };

  const addAttributeValue = (attributeIndex, value) => {
    if (!value.trim()) return;

    const updatedAttributes = [...attributes];
    const attribute = updatedAttributes[attributeIndex];
    
    if (attribute.gia_tri.includes(value.trim())) {
      message.warning('Giá trị này đã tồn tại');
      return;
    }

    attribute.gia_tri.push(value.trim());
    onAttributesChange(updatedAttributes);
    
    // Clear the input
    setNewAttributeValues(prev => ({
      ...prev,
      [attributeIndex]: ''
    }));
  };

  const removeAttributeValue = (attributeIndex, valueIndex) => {
    const updatedAttributes = [...attributes];
    updatedAttributes[attributeIndex].gia_tri.splice(valueIndex, 1);
    onAttributesChange(updatedAttributes);
  };

  const handleKeyPress = (e, attributeIndex) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const value = newAttributeValues[attributeIndex] || '';
      addAttributeValue(attributeIndex, value);
    }
  };

  const handleNewAttributeKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addAttribute();
    }
  };

  return (
    <Card 
      title={
        <Space>
          <span>Thuộc tính</span>
          <Tooltip title="Thêm mô tả thuộc tính sản phẩm có nhiều lựa chọn, thí dụ: có màu sắc...">
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
          </Tooltip>
        </Space>
      }
      style={{ marginBottom: 24 }}
    >
      <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
        Thêm mô tả thuộc tính sản phẩm có nhiều lựa chọn, thí dụ: có màu sắc...
      </Text>

      {/* Existing attributes */}
      {Array.isArray(attributes) && attributes.map((attribute, attributeIndex) => (
        <div key={attributeIndex} style={{ marginBottom: 16 }}>
          <Row gutter={16} align="top" style={{ marginBottom: 8 }}>
            <Col span={6}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '8px 12px',
                background: '#f5f5f5',
                borderRadius: '6px',
                border: '1px solid #d9d9d9',
                minHeight: '40px'
              }}>
                <Text strong style={{ fontSize: '14px' }}>{attribute.ten}</Text>
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => removeAttribute(attributeIndex)}
                  style={{ color: '#ff4d4f', padding: '2px' }}
                />
              </div>
            </Col>
            <Col span={18}>
              <div style={{
                minHeight: '40px',
                padding: '8px 12px',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                background: '#fafafa',
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'center',
                gap: '4px'
              }}>
                {Array.isArray(attribute?.gia_tri) && attribute.gia_tri.map((value, valueIndex) => (
                  <Tag
                    key={valueIndex}
                    closable
                    onClose={() => removeAttributeValue(attributeIndex, valueIndex)}
                    color="blue"
                    style={{
                      margin: '2px',
                      fontSize: '12px',
                      padding: '2px 8px',
                      borderRadius: '4px'
                    }}
                  >
                    {value}
                  </Tag>
                ))}
                <Input
                  size="small"
                  placeholder="Gõ ký tự và ấn Enter để thêm thuộc tính"
                  value={newAttributeValues[attributeIndex] || ''}
                  onChange={(e) => setNewAttributeValues(prev => ({
                    ...prev,
                    [attributeIndex]: e.target.value
                  }))}
                  onKeyPress={(e) => handleKeyPress(e, attributeIndex)}
                  style={{
                    width: '250px',
                    border: 'none',
                    background: 'transparent',
                    boxShadow: 'none',
                    fontSize: '12px'
                  }}
                  onPressEnter={(e) => {
                    const value = e.target.value;
                    addAttributeValue(attributeIndex, value);
                  }}
                />
              </div>
            </Col>
          </Row>
        </div>
      ))}

      {/* Add new attribute */}
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: '8px',
        marginTop: 16,
        padding: '12px',
        border: '1px dashed #d9d9d9',
        borderRadius: '6px',
        background: '#fafafa'
      }}>
        <PlusOutlined style={{ color: '#1890ff' }} />
        <Text style={{ color: '#1890ff', fontWeight: 500 }}>Thêm thuộc tính khác</Text>
        <Input
          placeholder="Nhập tên thuộc tính (VD: Màu sắc, Kích thước...)"
          value={newAttributeName}
          onChange={(e) => setNewAttributeName(e.target.value)}
          onKeyPress={handleNewAttributeKeyPress}
          style={{ flex: 1, maxWidth: '300px' }}
        />
        <Button 
          type="primary" 
          size="small"
          onClick={addAttribute}
          disabled={!newAttributeName.trim()}
        >
          Thêm
        </Button>
      </div>

      {attributes.length > 0 && (
        <div style={{ 
          marginTop: 16, 
          padding: '12px', 
          background: '#e6f7ff', 
          border: '1px solid #91d5ff',
          borderRadius: '6px' 
        }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <InfoCircleOutlined style={{ marginRight: '4px', color: '#1890ff' }} />
            Hệ thống sẽ tự động tạo các phiên bản sản phẩm dựa trên tổ hợp các thuộc tính bạn đã thêm.
            Ví dụ: Size (S, M, L) + Màu (Đỏ, Xanh) = 6 phiên bản sản phẩm.
          </Text>
        </div>
      )}
    </Card>
  );
};

export default InlineAttributeManager;
