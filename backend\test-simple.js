const http = require('http');

function testAPI() {
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/health',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('Response:', data);
      
      if (res.statusCode === 200) {
        console.log('✅ Server is running!');
        testDebtReport();
      } else {
        console.log('❌ Server health check failed');
      }
    });
  });

  req.on('error', (err) => {
    console.error('❌ Connection error:', err.message);
  });

  req.end();
}

function testDebtReport() {
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/debt/report',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`\nDebt Report Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('Debt Report Response:', data);
      
      if (res.statusCode === 401) {
        console.log('✅ API requires authentication (expected)');
      } else if (res.statusCode === 200) {
        console.log('✅ API working!');
      } else {
        console.log('❌ Unexpected response');
      }
    });
  });

  req.on('error', (err) => {
    console.error('❌ Debt report error:', err.message);
  });

  req.end();
}

console.log('🧪 Testing API...');
testAPI();
