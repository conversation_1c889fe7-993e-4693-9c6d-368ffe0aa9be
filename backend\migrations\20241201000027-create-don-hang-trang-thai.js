'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('don_hang_trang_thai', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      don_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'don_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      trang_thai: {
        type: Sequelize.ENUM('cho_xu_ly', 'da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'huy'),
        allowNull: false
      },
      thoi_gian: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      nhan_vien_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      ghi_chu: {
        type: Sequelize.TEXT,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('don_hang_trang_thai', ['don_hang_id']);
    await queryInterface.addIndex('don_hang_trang_thai', ['trang_thai']);
    await queryInterface.addIndex('don_hang_trang_thai', ['thoi_gian']);
    await queryInterface.addIndex('don_hang_trang_thai', ['nhan_vien_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('don_hang_trang_thai');
  }
};
