import React from 'react';
import { Card, Form, Input, Switch, Button, Row, Col, message, Breadcrumb } from 'antd';
import { useNavigate } from 'react-router-dom';
import { 
  ShopOutlined, 
  EnvironmentOutlined, 
  FileTextOutlined, 
  SaveOutlined,
  ArrowLeftOutlined,
  HomeOutlined
} from '@ant-design/icons';
import { useCreateWarehouse } from '../../hooks/useWarehouses';

const { TextArea } = Input;

const AddWarehouse = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const createWarehouseMutation = useCreateWarehouse();

  const handleSubmit = async (values) => {
    try {
      const submitData = {
        ...values,
        trang_thai: values.trang_thai ? 'active' : 'inactive'
      };
      
      await createWarehouseMutation.mutateAsync(submitData);
      message.success('Tạo kho hàng thành công!');
      navigate('/warehouses');
    } catch (error) {
      console.error('Error creating warehouse:', error);
      message.error('C<PERSON> lỗi xảy ra khi tạo kho hàng!');
    }
  };

  const handleCancel = () => {
    navigate('/warehouses');
  };

  return (
    <div>
      {/* Breadcrumb */}
      <Breadcrumb style={{ marginBottom: 24 }}>
        <Breadcrumb.Item>
          <HomeOutlined />
          <span>Trang chủ</span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <ShopOutlined />
          <span>Kho hàng</span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>Thêm kho hàng</Breadcrumb.Item>
      </Breadcrumb>

      {/* Main Content */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <ShopOutlined />
            Thêm kho hàng mới
          </div>
        }
        extra={
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handleCancel}
          >
            Quay lại
          </Button>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            trang_thai: true
          }}
          style={{ maxWidth: 800 }}
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="Tên kho hàng"
                name="ten_kho"
                rules={[
                  { required: true, message: 'Vui lòng nhập tên kho hàng!' },
                  { min: 2, message: 'Tên kho hàng phải có ít nhất 2 ký tự!' },
                  { max: 100, message: 'Tên kho hàng không được quá 100 ký tự!' }
                ]}
              >
                <Input
                  prefix={<ShopOutlined />}
                  placeholder="Nhập tên kho hàng"
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Trạng thái"
                name="trang_thai"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="Hoạt động"
                  unCheckedChildren="Ngừng hoạt động"
                  size="default"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="Địa chỉ"
            name="dia_chi"
            rules={[
              { required: true, message: 'Vui lòng nhập địa chỉ kho hàng!' },
              { min: 5, message: 'Địa chỉ phải có ít nhất 5 ký tự!' },
              { max: 255, message: 'Địa chỉ không được quá 255 ký tự!' }
            ]}
          >
            <Input
              prefix={<EnvironmentOutlined />}
              placeholder="Nhập địa chỉ kho hàng"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="Mô tả"
            name="mo_ta"
            rules={[
              { max: 500, message: 'Mô tả không được quá 500 ký tự!' }
            ]}
          >
            <TextArea
              rows={4}
              placeholder="Nhập mô tả về kho hàng (tùy chọn)"
              showCount
              maxLength={500}
            />
          </Form.Item>

          {/* Action Buttons */}
          <Form.Item style={{ marginTop: 32 }}>
            <Row gutter={16}>
              <Col>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  size="large"
                  loading={createWarehouseMutation.isLoading}
                >
                  Tạo kho hàng
                </Button>
              </Col>
              <Col>
                <Button
                  size="large"
                  onClick={handleCancel}
                  disabled={createWarehouseMutation.isLoading}
                >
                  Hủy
                </Button>
              </Col>
            </Row>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default AddWarehouse;
